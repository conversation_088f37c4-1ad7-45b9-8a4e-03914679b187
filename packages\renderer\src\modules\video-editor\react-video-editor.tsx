import React from 'react'

import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable'
import { Materials } from '@/modules/video-editor/components/materials'
import { Editor } from '@/modules/video-editor/components/core/editor'

import {
  AssetLoading<PERSON>rovider,
  CachedOverlaysProvider,
  EditorProvider,
  SidebarProvider as EditorSidebarProvider,
  TimelineProvider
} from './contexts'
import { EditorDndWrapper } from '@/modules/video-editor/components/editor-dnd-wrapper'

export default React.memo(
  function ReactVideoEditor({ scriptId, projectId }: { scriptId: string, projectId: string }) {
    return (
      <EditorSidebarProvider>
        <EditorProvider scriptId={scriptId} projectId={projectId}>
          <TimelineProvider>
            <CachedOverlaysProvider>
              <AssetLoadingProvider>
                <EditorDndWrapper>
                  <div className="flex flex-col size-full">
                    <div className="w-full">
                      <Editor.Header />
                    </div>

                    <div className="flex-1 w-full flex">
                      <Materials.Sidebar />

                      <ResizablePanelGroup direction="vertical" className="h-full flex-1">
                        <ResizablePanel defaultSize={50} minSize={50}>
                          <ResizablePanelGroup direction="horizontal">
                            <ResizablePanel defaultSize={24} minSize={15} maxSize={30}>
                              <Materials.Content />
                            </ResizablePanel>
                            <ResizableHandle />

                            <ResizablePanel defaultSize={60} minSize={40}>
                              <Editor.Player />
                            </ResizablePanel>
                            <ResizableHandle />

                            <ResizablePanel defaultSize={22} minSize={15} maxSize={30}>
                              <Editor.Operation />
                            </ResizablePanel>
                          </ResizablePanelGroup>
                        </ResizablePanel>
                        <ResizableHandle />

                        <ResizablePanel defaultSize={50} minSize={5}>
                          <div className="h-full overflow-y-hidden flex flex-col">
                            <Editor.Toolbar />
                            <Editor.Timeline />
                          </div>
                        </ResizablePanel>
                      </ResizablePanelGroup>
                    </div>
                  </div>
                </EditorDndWrapper>
              </AssetLoadingProvider>
            </CachedOverlaysProvider>
          </TimelineProvider>
        </EditorProvider>
      </EditorSidebarProvider>
    )
  }
)


export interface AuthState {
  accessToken: string;
  expiresTime: number;
  openid: string;
  refreshToken: string;
  userId: number;
}

/**
 * 团队状态接口
 */
export interface TeamState {
  /** 当前团队ID */
  tenantId?: number
  /** 团队信息 */
  teamInfo?: {
    id: number
    name: string
    contactName: string
    contactUserId: number
    status: number
  }
}

/**
 * 统一请求状态接口
 */
export interface IPCRequestState {
  /** 认证状态 */
  auth: AuthState
  /** 团队状态 */
  team: TeamState
  /** 基础URL */
  baseUrl?: string
}

export interface IPCRequestConfig {
  /** 请求头 */
  headers?: Record<string, string>
  /** 超时时间（毫秒） */
  timeout?: number
  
}

/**
 * 请求状态管理器接口
 */
export interface RequestStateManager {
  /** 获取当前状态 */
  getState(): IPCRequestState

  /** 更新认证状态 */
  updateAuthState(auth: Partial<AuthState>): void

  /** 更新团队状态 */
  updateTeamState(team: Partial<TeamState>): void

  /** 清除所有状态 */
  clearState(): void

  /** 检查token是否过期 */
  isTokenExpired(): boolean
}

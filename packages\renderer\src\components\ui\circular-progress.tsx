import React from 'react'
import { cn } from '@/components/lib/utils'

interface CircularProgressProps {
  /**
   * 进度值 (0-100)
   */
  value: number
  /**
   * 圆环大小
   */
  size?: number
  /**
   * 圆环粗细
   */
  strokeWidth?: number
  /**
   * 进度条颜色
   */
  color?: string
  /**
   * 背景圆环颜色
   */
  backgroundColor?: string
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 是否显示百分比文本
   */
  showPercentage?: boolean
  /**
   * 自定义中心内容
   */
  children?: React.ReactNode
}

/**
 * 圆环进度条组件
 */
export function CircularProgress({
  value,
  size = 120,
  strokeWidth = 8,
  color = 'hsl(var(--primary))',
  backgroundColor = 'hsl(var(--muted))',
  className,
  showPercentage = true,
  children,
}: CircularProgressProps) {
  const radius = (size - strokeWidth) / 2
  const circumference = radius * 2 * Math.PI
  const strokeDasharray = circumference
  const strokeDashoffset = circumference - (value / 100) * circumference

  return (
    <div
      className={cn('relative inline-flex items-center justify-center', className)}
      style={{ width: size, height: size }}
    >
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        {/* 背景圆环 */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={backgroundColor}
          strokeWidth={strokeWidth}
          fill="none"
        />
        {/* 进度圆环 */}
        <circle
          cx={size / 2}
          cy={size / 2}
          r={radius}
          stroke={color}
          strokeWidth={strokeWidth}
          fill="none"
          strokeDasharray={strokeDasharray}
          strokeDashoffset={strokeDashoffset}
          strokeLinecap="round"
          className="transition-all duration-300 ease-in-out"
        />
      </svg>
      
      {/* 中心内容 */}
      <div className="absolute inset-0 flex items-center justify-center">
        {children || (showPercentage && (
          <span className="text-sm font-medium text-foreground">
            {Math.round(value)}%
          </span>
        ))}
      </div>
    </div>
  )
}

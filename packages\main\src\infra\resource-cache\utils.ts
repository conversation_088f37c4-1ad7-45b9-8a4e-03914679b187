import crypto from 'crypto'

/**
 * 生成 URL 的 SHA1 hash 作为资源的唯一 key
 * @param url 资源 URL
 * @returns SHA1 hash 字符串
 */
export function hashUrl(url: string): string {
  return crypto.createHash('sha1').update(url).digest('hex').slice(0, 12)
}

/**
 * 获取文件扩展名
 * @param url 资源 URL
 * @returns 文件扩展名，不包含点，如果无法获取则返回空字符串
 */
export function getFileExtension(url: string): string {
  const parts = url.split('.')
  if (parts.length > 1) {
    return parts.pop() || ''
  }
  return ''
}

/**
 * 检查资源是否过期 (简易判断，可根据实际需求扩展)
 * @param entry 缓存条目
 * @param maxAgeSeconds 最大缓存时间（秒）
 * @returns 是否过期
 */
export function isCacheEntryExpired(entry: { downloadedAt: number }, maxAgeSeconds: number): boolean {
  const now = Date.now()
  return (now - entry.downloadedAt * 1000) > (maxAgeSeconds * 1000)
}

/**
 * 检查文件是否存在并可访问
 * @param filePath 文件路径
 * @returns Promise<boolean> 是否存在
 */
import { promises as fsPromises } from 'fs'

export async function checkFileExists(filePath: string): Promise<boolean> {
  try {
    await fsPromises.access(filePath)
    return true
  } catch {
    return false
  }
} 
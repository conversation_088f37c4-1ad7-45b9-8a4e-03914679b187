import React, { useCallback, useState } from 'react'
import { ModalFooterProps, ModalContent } from '../modal'
import { useModal, useModalContext } from '@/libs/tools/modal'

type Props = Pick<ModalFooterProps, 'buttons'> & {
  kind: string
  name: string
  danger?: boolean
  action?: () => Promise<any> | any
  actionName?: string
}

function DeleteModal({ kind, name, danger, action, actionName = '删除', buttons }: Props) {
  const [pending, setPending] = useState(false)
  const { close } = useModalContext()

  return (
    <ModalContent
      title={`${actionName}${kind}`}
      description={`确定要${actionName}${kind}「${name}」吗？${danger ? '此操作不可恢复！' : ''}`}
      pending={pending}
      onConfirm={async () => {
        setPending(true)
        try {
          await action?.()
          close()
        } finally {
          setPending(false)
        }
      }}
      buttons={buttons}
    />
  )
}

export function useDeleteModal() {
  const modal = useModal()
  return useCallback((props: Props) => modal({ content: <DeleteModal {...props} /> }), [])
}

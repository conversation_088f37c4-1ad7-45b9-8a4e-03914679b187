import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react'
import { ScriptSceneData, useQueryScript } from '@/hooks/queries/useQueryScript'
import { useEditorContext } from '@/modules/video-editor/contexts'
import { Button } from '@/components/ui/button'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Download, Edit, Mic2 } from 'lucide-react'
import useVirtualTabsStore from '@/libs/stores/useVirtualTabsStore'
import { LoadingIndicator } from '@/components/LoadingIndicator'
import { estimateSentencesDuration, extractSentencesFromScriptScene } from '@/libs/tools/script'
import { OverlayType, StoryboardOverlay } from '@clipnest/remotion-shared/types'
import { generateNewOverlayId } from '@/modules/video-editor/utils/track-helper'
import { DEFAULT_OVERLAY, FPS } from '@/modules/video-editor/constants'
import { EVENT_NAMES, useEventListener } from '@/modules/video-editor/hooks/useEventBus'
import { cn } from '@/components/lib/utils'
import { useOverlayHelper } from '@/modules/video-editor/hooks/helpers/useOverlayHelper'
import { useOperationPanelStore } from '@/modules/video-editor/stores/useOperationPanelStore'
import { Track, TrackType } from '@/modules/video-editor/types'

// 脚本段落组件
export const ScriptScene: React.FC<{
  scene: ScriptSceneData
  index: number
}> = ({ scene, index }) => {
  // 将脚本内容按行分割成句子
  const sentences = useMemo(() => extractSentencesFromScriptScene(scene), [scene])

  const [minDuration, maxDuration] = estimateSentencesDuration(sentences)

  return (
    <div className="mb-6">
      {/* 段落标题 */}
      <div className="flex items-center gap-2 mb-3">
        <h3 className="text-md font-medium">
          #{index + 1} {scene.title || '未命名分镜'}
        </h3>
        {minDuration && (
          <div className="text-xs text-gray-500">
            预计时长 {minDuration}s {maxDuration && `~ ${maxDuration}s`}
          </div>
        )}
      </div>

      {/* 句子列表 */}
      <SentenceList sentences={sentences} />

      {/* 画面说明 */}
      {scene.notes && (
        <div className="mt-3">
          <div className="text-xs font-medium text-gray-700 mb-1">画面说明</div>
          <div className="text-xs text-gray-600  p-2 rounded">
            {scene.notes}
          </div>
        </div>
      )}

      {/* 拍摄示例 */}
      {/*<div className="mt-3">*/}
      {/*  <div className="text-xs font-medium text-gray-700 mb-1">拍摄示例  </div>*/}
      {/*  <div className="text-xs text-gray-600" />*/}
      {/*</div>*/}
    </div>
  )
}

// 句子列表组件
export const SentenceList: React.FC<{
  sentences: string[]
}> = ({ sentences }) => {
  return (
    <div className="space-y-1">
      {sentences.map((sentence, index) => (
        <div key={index} className="flex items-start gap-2 text-xs">
          <span className="text-gray-400 text-xs mt-0.5 min-w-[20px]">
            {index + 1}.
          </span>
          <span className="text-gray-400 leading-relaxed">
            {sentence}
          </span>
        </div>
      ))}
    </div>
  )
}

/**
 * 计算句子的时长（帧数）
 */
const calculateSentenceDurationInFrames = (sentence: string): number => {
  // 简单预估10个字符为 1 秒，转换为帧数
  const durationInSeconds = sentence.length / 10
  return Math.max(FPS * durationInSeconds, FPS) // 最少1秒
}

const useGenerateTextOnly = (scenes: ScriptSceneData[]) => {
  const { tracks, updateTracks } = useEditorContext()
  const { generateDefaultCaptionOverlay } = useOverlayHelper()

  /**
   * 生成分镜轨道和口播轨道
   */
  const generateTracksFromScript = useCallback(
    (scenes: ScriptSceneData[], existingTracks: Track[]): Track[] => {
      const MINIMUM_DURATION = FPS

      if (!scenes.length) return []

      // 获取下一个可用的 overlay ID
      let nextOverlayId = generateNewOverlayId(existingTracks)

      // 1. 生成分镜轨道
      const storyboardTrack: Track = {
        type: TrackType.STORYBOARD,
        overlays: []
      }

      let currentFrame = 0

      // 为每个 scene 创建分镜 overlay
      scenes.forEach(scene => {
        const sentences = extractSentencesFromScriptScene(scene)

        // 计算该分镜的时长：取所有句子预估时长的最大值
        const [minDuration, maxDuration = minDuration] = estimateSentencesDuration(sentences)
        const sceneDurationInFrames = maxDuration
          ? Math.max(maxDuration * FPS, MINIMUM_DURATION)
          : MINIMUM_DURATION

        const storyboardOverlay: StoryboardOverlay = {
          ...DEFAULT_OVERLAY,
          id: nextOverlayId++,
          type: OverlayType.STORYBOARD,
          from: currentFrame,
          durationInFrames: sceneDurationInFrames,
        }

        storyboardTrack.overlays.push(storyboardOverlay)
        currentFrame += sceneDurationInFrames
      })

      // 2. 计算需要的口播轨道数量（所有 scenes 中的最大句子数量）
      const sentenceCounts = scenes.map(scene =>
        extractSentencesFromScriptScene(scene).length
      )
      const maxSentenceCount = sentenceCounts.length > 0 ? Math.max(...sentenceCounts) : 0

      // 3. 生成口播轨道
      const narrationTracks: Track[] = []

      for (let trackIndex = 0; trackIndex < maxSentenceCount; trackIndex++) {
        const narrationTrack: Track = {
          type: TrackType.NARRATION,
          overlays: []
        }

        let sceneStartFrame = 0

        // 为每个 scene 在当前口播轨道中添加对应的句子
        scenes.forEach((scene, sceneIndex) => {
          const sentences = extractSentencesFromScriptScene(scene)
          const sentence = sentences[trackIndex] // 第 trackIndex 个句子

          if (sentence && sentence.trim()) {
            // 计算句子时长
            const sentenceDurationInFrames = calculateSentenceDurationInFrames(sentence)

            narrationTrack.overlays.push(
              generateDefaultCaptionOverlay({
                id: nextOverlayId++,
                content: sentence.trim(),
                storyboardIndex: sceneIndex, // 关联到对应的分镜
                from: sceneStartFrame,
                durationInFrames: sentenceDurationInFrames,
              })
            )
          }

          // 更新下一个分镜的起始帧位置
          const storyboardOverlay = storyboardTrack.overlays[sceneIndex]
          if (storyboardOverlay) {
            sceneStartFrame += storyboardOverlay.durationInFrames
          }
        })

        narrationTracks.push(narrationTrack)
      }

      return [storyboardTrack, ...narrationTracks]
    },
    [generateDefaultCaptionOverlay]
  )

  return useCallback(() => {
    if (!scenes.length) {
      console.warn('没有可导入的脚本内容')
      return
    }

    // 生成新的分镜轨道和口播轨道
    const newTracks = generateTracksFromScript(scenes, tracks)

    if (!newTracks.length) {
      console.warn('生成的轨道为空')
      return
    }

    // 替换现有的分镜轨道和口播轨道，保留其他类型轨道
    updateTracks(prevTracks => {
      const filteredTracks = prevTracks.filter(track =>
        track.type !== TrackType.STORYBOARD && track.type !== TrackType.NARRATION
      )

      return [...newTracks, ...filteredTracks]
    })
  }, [scenes, tracks, updateTracks, generateTracksFromScript])
}

const ScriptPanel = () => {
  const { scriptId } = useEditorContext()
  const { data: script } = useQueryScript(scriptId)
  const { pushNamedTab } = useVirtualTabsStore()
  const { openTextToSpeechPanel } = useOperationPanelStore()

  // 高光特效状态
  const [isHighlighted, setIsHighlighted] = useState(false)

  // 监听高光事件
  useEventListener(EVENT_NAMES.HIGHLIGHT_SCRIPT_BUTTONS, () => {
    setIsHighlighted(true)

    const fadeOutTimer = setTimeout(() => {
      setIsHighlighted(false)
    }, 2000)

    return () => clearTimeout(fadeOutTimer)
  })

  // 组件卸载时清理状态
  useEffect(() => {
    return () => {
      setIsHighlighted(false)
    }
  }, [])

  // 高光特效样式类
  const highlightClasses = cn(
    'text-xs h-7 px-3 relative z-10 transition-all duration-600',
    isHighlighted && `
      bg-gradient-brand text-white border-transparent shadow-xl hover:opacity-90
      opacity-100 scale-100 animate-pulse ring-4 ring-[var(--color-primary-highlight)]/30
    `
  )

  // 高光容器样式（用于外层包装）
  const highlightContainerClasses = isHighlighted
    ? cn(
      'relative transition-all duration-600',
      'before:absolute before:-inset-1 before:bg-gradient-brand before:rounded-md before:opacity-20 before:animate-ping before:pointer-events-none'
    )
    : ''

  // 处理按钮点击时取消高光
  const handleButtonClick = (originalHandler?: () => void) => {
    if (isHighlighted) {
      setTimeout(() => {
        setIsHighlighted(false)
        originalHandler?.()
      }, 300) // 快速淡出
    } else {
      // 如果没有高光或已经在淡出，直接执行
      originalHandler?.()
    }
  }

  const generateTextOnly = useGenerateTextOnly(script?.scenes || [])

  if (!script) return <LoadingIndicator />

  return (
    <div className="h-full flex flex-col w-full">
      {/* 顶部工具栏 */}
      <div className="flex items-center p-4 border-b border-gray-200">
        <div className="flex-grow">
          {script.title}
        </div>

        <Button
          variant="ghost"
          size="sm"
          className="text-xs h-7 px-3 text-gray-400"
          onClick={() => pushNamedTab('Script', { id: script.id.toString() })}
        >
          <Edit className="w-3 h-3 mr-1" />
          编辑脚本
        </Button>

        <div className={cn('flex gap-1', highlightContainerClasses)}>
          <div className={cn('relative', )}>
            <Button
              variant="outline"
              size="sm"
              className={cn(highlightClasses)}
              onClick={() => handleButtonClick(generateTextOnly)}
            >
              <Download className="w-3 h-3 mr-1" />
              仅导入台词
            </Button>
          </div>

          <div className={cn('relative')}>
            <Button
              variant="outline"
              size="sm"
              className={cn(highlightClasses)}
              onClick={() => handleButtonClick(() => openTextToSpeechPanel(script.scenes))}
            >
              <Mic2 className="w-3 h-3 mr-1" />
              一键生成配音
            </Button>
          </div>
        </div>

      </div>

      {/* 脚本内容区域 */}
      <ScrollArea className="flex-1">
        <div className="p-4">
          {script.scenes.length > 0 ? (
            script.scenes.map((scene, index) => (
              <ScriptScene
                key={scene.id}
                scene={scene}
                index={index}
              />
            ))
          ) : (
            <div className="flex flex-col items-center justify-center h-40 text-gray-500">
              <div className="text-sm">暂无脚本内容</div>
              <div className="text-xs mt-1">请先添加脚本内容</div>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  )
}

export default React.memo(ScriptPanel)

import React from 'react'
import { Img, useCurrentFrame } from 'remotion'
import { StickerOverlay } from '@clipnest/remotion-shared/types'
import { animationTemplates } from '@clipnest/remotion-shared/constants'
import { toAbsoluteUrl } from '@clipnest/remotion-shared/utils'
import { useRenderContext } from '../../render.context'

interface ImageLayerContentProps {
  overlay: StickerOverlay
  baseUrl?: string
}

export const ImageLayerContent: React.FC<ImageLayerContentProps> = ({
  overlay,
  baseUrl,
}) => {
  const { playerMetadata: { fps } } = useRenderContext()
  const frame = useCurrentFrame()
  const isExitPhase = frame >= overlay.durationInFrames - fps

  const enterAnimation
    = !isExitPhase && overlay.styles.animation?.enter
      ? animationTemplates[overlay.styles.animation.enter]?.enter(
        frame,
        overlay.durationInFrames,
      )
      : {}

  const exitAnimation
    = isExitPhase && overlay.styles.animation?.exit
      ? animationTemplates[overlay.styles.animation.exit]?.exit(
        frame,
        overlay.durationInFrames,
      )
      : {}

  const imageStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    objectPosition: overlay.styles.objectPosition,
    opacity: overlay.styles.opacity,
    transform: overlay.styles.transform || 'none',
    filter: overlay.styles.filter || 'none',
    borderRadius: overlay.styles.borderRadius || '0px',
    boxShadow: overlay.styles.boxShadow || 'none',
    border: overlay.styles.border || 'none',
    ...(isExitPhase ? exitAnimation : enterAnimation),
  }

  /**
   * Create a container style that includes padding and background color
   */
  const containerStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    padding: overlay.styles.padding || '0px',
    backgroundColor: overlay.styles.paddingBackgroundColor || 'transparent',
    display: 'flex', // Use flexbox for centering
    alignItems: 'center',
    justifyContent: 'center',
  }

  // Determine the image source URL
  let imageSrc = overlay.src

  // If it's a relative URL and baseUrl is provided, use baseUrl
  if (overlay.src.startsWith('/') && baseUrl) {
    imageSrc = `${baseUrl}${overlay.src}`
  }
  // Otherwise use the toAbsoluteUrl helper for relative URLs
  else if (overlay.src.startsWith('/')) {
    imageSrc = toAbsoluteUrl(overlay.src)
  }

  return (
    <div style={containerStyle}>
      <Img src={imageSrc} style={imageStyle} alt="" />
    </div>
  )
}

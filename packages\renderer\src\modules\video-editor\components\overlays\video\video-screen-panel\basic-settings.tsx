import React from 'react'
import { FormNumberInput, FormSlider, SectionTitle } from '../../common/form-components'
import { MediaPaddingControls } from '../../common/media-padding-controls'
import { FPS } from '../../../../constants'
import { Label } from '@/components/ui/label'
import { VideoFadeControls } from '../../common/fade-controls'
import { Checkbox } from '@/components/ui/checkbox'
import { MediaControls } from '../../common/media-controls'
import { useOverlayEditing } from '@/modules/video-editor/contexts'
import { VideoOverlay } from '@clipnest/remotion-shared/types'

export function BasicSettings() {
  const { localOverlay: videoOverlay, requestUpdate: updateOverlay } = useOverlayEditing<VideoOverlay>()

  return (
    <div className="space-y-2 w-full">
      {/* 大小 */}
      <div className="overlay-setting-card">
        <SectionTitle title="大小" />
        <MediaPaddingControls
          localOverlay={videoOverlay}
          handleStyleChange={updateOverlay}
        />
      </div>

      {/* 位置 */}
      <div className="overlay-setting-card">
        <SectionTitle title="位置" />
        <div className="grid grid-cols-2 gap-3">
          <FormNumberInput
            label="X"
            value={videoOverlay.left}
            onChange={val => updateOverlay({ left: val }, true)}
            decimalPlaces={2}
          />
          <FormNumberInput
            label="Y"
            value={videoOverlay.top}
            onChange={val => updateOverlay({ top: val }, true)}

            decimalPlaces={2}
          />
        </div>
      </div>

      {/* 旋转 */}
      <div className="overlay-setting-card">
        <SectionTitle title="旋转" />
        <FormNumberInput
          value={videoOverlay.rotation}
          onChange={val => updateOverlay({ rotation: val }, true)}
          decimalPlaces={2}
        />
      </div>

      {/* 基础 */}
      <div className="overlay-setting-card">
        <SectionTitle title="基础" />
        <FormSlider
          max={1}
          min={0}
          step={0.1}
          value={videoOverlay?.styles?.opacity ?? 1}
          onChange={(val, commit) => updateOverlay({ styles: { opacity: val } }, commit)}
          label="不透明度"
        />
      </div>

      {/* 音量 */}
      <div className="overlay-setting-card">
        <SectionTitle title="音量" />
        <FormSlider
          value={videoOverlay?.styles?.volume ?? 1}
          onChange={(val, commit) => updateOverlay({ styles: { volume: val } }, commit)}
          max={1}
          min={0}
          step={0.1}
        />
        <div className="gap-6 flex items-center mt-2">
          <div className="flex items-center gap-2">
            <Checkbox id="gain" />
            <Label htmlFor="gain">音频增益</Label>
          </div>
          <div className="flex items-center gap-2">
            <Checkbox id="reduction" />
            <Label htmlFor="reduction">音频降噪</Label>
          </div>
        </div>
      </div>

      {/* 淡入淡出控制 */}
      <div className="overlay-setting-card">
        <VideoFadeControls
          overlay={videoOverlay}
          onOverlayChange={updateOverlay}
        />
      </div>

      {/* 视频裁剪 */}
      <div className="overlay-setting-card">
        <SectionTitle
          title="视频裁剪"
          onReset={() => {
            updateOverlay({
              trimStart: 0,
              trimEnd: 0
            }, true)
          }}
        />
        <div className="grid grid-cols-2 gap-3">
          <FormNumberInput
            label="去片头"
            suffix="秒"
            value={videoOverlay.trimStart ?? 0}
            onChange={value => updateOverlay({ trimStart: value }, true)}
            min={0}
            max={Math.max(0, (videoOverlay.durationInFrames / FPS) - (videoOverlay.trimEnd ?? 0) - 1)}
            step={0.1}
            decimalPlaces={2}
            formatDisplay={true}
          />
          <FormNumberInput
            label="去片尾"
            suffix="秒"
            value={videoOverlay.trimEnd ?? 0}
            onChange={value => updateOverlay({ trimEnd: value }, true)}
            min={0}
            max={Math.max(0, (videoOverlay.durationInFrames / FPS) - (videoOverlay.trimStart ?? 0) - 1)}
            step={0.1}
            decimalPlaces={2}
            formatDisplay={true}
          />
        </div>
        {/* 显示裁剪后的视频时长 */}
        {(videoOverlay.trimStart || videoOverlay.trimEnd) && (
          <div className="flex justify-between text-xs text-muted-foreground  p-2 rounded-md">
            <span>裁剪后时长:</span>
            <span className="font-medium">
              {(
                (videoOverlay.durationInFrames / FPS) -
                  (videoOverlay.trimStart ?? 0) -
                  (videoOverlay.trimEnd ?? 0)
              ).toFixed(2)}秒
            </span>
          </div>
        )}
      </div>

      {/* 变速和时长控制 */}
      <MediaControls
        overlay={videoOverlay}
        onOverlayPropertyChange={updateOverlay}
        minSpeed={0.25}
        maxSpeed={4}
        speedStep={0.25}
        minDuration={0.1}
        maxDuration={videoOverlay.originalDurationInFrames / FPS}
        durationStep={0.1}
      />
    </div>
  )
}

import React, { PropsWithChildren } from 'react'
import { MultiSelection } from '@/contexts'
import { clsx } from 'clsx'
import { Check } from 'lucide-react'

export const MultiSelectableCard: React.FC<PropsWithChildren<MultiSelection & { index: number }>> = ({
  selectedIndices,
  toggleSelection,
  children,
  index
}) => {
  const isSelected = selectedIndices.has(index)

  return (
    <div
      className={clsx(
        'relative rounded-sm',
        isSelected ? 'outline-primary outline-2' : 'outline-transparent',
      )}
      onClick={() => toggleSelection(index)}
    >
      {/* 选中状态勾选图标 */}
      {isSelected && (
        <div className="z-9 absolute top-2 left-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
          <Check className="w-4 h-4 text-white" />
        </div>
      )}

      {children}
    </div>
  )
}

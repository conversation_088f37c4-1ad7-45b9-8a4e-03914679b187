import { useState } from 'react'

export function useAction<Args extends any[], Ret>(fn: (...args: Args) => Ret | Promise<Ret>) {
  const [data, setData] = useState<Ret | undefined>(undefined)
  const [isError, setIsError] = useState(false)
  const [isPending, setIsPending] = useState(false)

  async function action(...args: Args) {
    setIsPending(true)
    try {
      const v = await fn(...args)
      setData(v)
    } catch {
      setIsError(true)
    } finally {
      setIsPending(false)
    }
  }

  return { action, data, isPending, isError }
}

import { LoaderCircleIcon } from 'lucide-react'
import React, { FC, ReactNode } from 'react'

import { twMerge } from 'tailwind-merge'

interface IconWithStatusProps {
  children: ReactNode;
  loading: boolean;
  className?: string;
  spinnerClassName?: string;
}

export const IconWithStatus: FC<IconWithStatusProps> = ({ children, loading, className, spinnerClassName }) => {
  return (
    <div className={twMerge('relative', className)}>

      {loading ? (
        <div className=" animate-spin ">
          <LoaderCircleIcon  className={ twMerge('text-white', spinnerClassName)}  />
        </div>
      ) : (
        children
      )}
    </div>
  )
}

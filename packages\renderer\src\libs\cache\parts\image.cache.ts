import { SubCacheManager } from '../types'

export class ImageCacheManager extends SubCacheManager {

  /**
   * 设置预加载图片
   */
  async setPreloadedImage(url: string, imageData: string): Promise<void> {
    await this.store.setItem(url, {
      data: imageData,
      timestamp: Date.now()
    })
  }

  /**
   * 获取预加载图片
   */
  async getPreloadedImage(url: string): Promise<string | null> {
    const entry = await this.store.getItem<{ data: string; timestamp: number }>(url)
    return entry?.data || null
  }

  /**
   * 检查图片是否已预加载
   */
  async isImagePreloaded(url: string): Promise<boolean> {
    const entry = await this.store.getItem(url)
    return entry !== null
  }

  async cleanup(now: number, maxAge: number) {
    // 清理预加载图片缓存
    const imageKeysToRemove: string[] = []
    await this.store.iterate((entry: { data: string; timestamp: number }, key: string) => {
      if (now - entry.timestamp > maxAge) {
        imageKeysToRemove.push(key)
      }
    })

    for (const key of imageKeysToRemove) {
      await this.store.removeItem(key)
    }
  }
}

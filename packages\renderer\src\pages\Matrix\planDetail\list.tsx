import React, { useState, useMemo } from 'react'
import { createSearchParams, useParams } from 'react-router'
import { ColumnDef } from '@tanstack/react-table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { DataTable } from '@/components/ui/data-table'
import { SearchIcon, RotateCcw } from 'lucide-react'
import { usePaginationPlanDetailList } from '@/hooks/queries/useQueryMatrix'
import { formatTimestamp } from '@/components/lib/utils'
import { DateRangePicker } from '@/components/date-range-picker'
import { type DateRange } from 'react-day-picker'
import { AccountPushDetail } from '@/types/matrix/douyin'
import { useNavigate } from 'react-router-dom'

// 发布状态映射
const publishStatusMap = {
  0: { label: '待发布', variant: 'secondary' as const },
  1: { label: '发布成功', variant: 'default' as const },
  2: { label: '发布失败', variant: 'destructive' as const },
  3: { label: '发布中', variant: 'secondary' as const },
}

export const PlanDetailList = () => {
  const { planId } = useParams()
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [dateRange, setDateRange] = useState<DateRange | undefined>()

  const navigate = useNavigate()

  const searchParams = useMemo(() => {
    const createTime = dateRange?.from && dateRange?.to
      ? [dateRange.from.getTime(), dateRange.to.getTime()] as [number, number]
      : undefined

    return {
      planIds: [Number(planId)],
      status: statusFilter !== 'all' ? Number(statusFilter) : undefined,
      createTime,
      search: searchTerm
    }
  }, [planId, statusFilter, dateRange])

  const handleNavigateToCreatePlan = async(planId?: number) => {
    if (!planId) return

    navigate({
      pathname: '/home/<USER>/distribution',
      search: createSearchParams({
        planId: planId.toString()
      }).toString()
    })
  }

  const {
    data: taskList,
    pagination,
    isLoading,
    setPagination,
    refetch
  } = usePaginationPlanDetailList(
    searchParams,
    20
  )

  const columns: ColumnDef<AccountPushDetail>[] = [
    {
      accessorKey: 'id',
      header: '任务ID',
      cell: ({ row }) => (
        <div className="font-mono text-sm">
          {row.getValue('id')}
        </div>
      ),
      size: 100,
    },
    {
      id: 'video',
      header: '视频',
      cell: ({ row }) => {
        const item = row.original
        return (
          <div className="flex items-center space-x-3">
            <div className="w-16 h-12  rounded overflow-hidden">
              {item.videoCover ? (
                <img
                  src={item.videoCover}
                  alt="视频封面"
                  className="w-full h-full object-cover"
                />
              ) : (
                <div className="w-full h-full flex items-center justify-center text-xs">
                  无封面
                </div>
              )}
            </div>
          </div>
        )
      },
      size: 80,
    },
    {
      accessorKey: 'title',
      header: '标题',
      cell: ({ row }) => {
        const title = row.getValue('title') as string
        return (
          <div className="max-w-[200px]">
            <div className="font-medium truncate">{title || '--'}</div>
          </div>
        )
      },
    },
    {
      accessorKey: 'nickname',
      header: '账号',
      cell: ({ row }) => {
        const nickname = row.getValue('nickname') as string
        return (
          <div className="font-medium">{nickname || '--'}</div>
        )
      },
    },
    {
      accessorKey: 'status',
      header: '发布状态',
      cell: ({ row }) => {
        const status = row.getValue('status') as number
        const statusInfo = publishStatusMap[status as keyof typeof publishStatusMap]

        if (!statusInfo) {
          return <Badge variant="secondary">未知状态</Badge>
        }

        return (
          <Badge variant={statusInfo.variant}>
            {statusInfo.label}
          </Badge>
        )
      },
    },
    {
      accessorKey: 'createTime',
      header: '创建时间',
      cell: ({ row }) => {
        const createTime = row.getValue('createTime') as number
        return (
          <div className="text-sm  min-w-[120px]">
            {createTime ? formatTimestamp(createTime) : '--'}
          </div>
        )
      },
    },
    {
      id: 'actions',
      header: '操作',
      cell: ({ row }) => {
        const item = row.original
        return (
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              disabled={!item.url}
            >
              查看视频
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleNavigateToCreatePlan(item.id)}
            >
              重新发布
            </Button>
          </div>
        )
      },
    },
  ]

  if (!planId) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">无效的计划ID</div>
      </div>
    )
  }

  return (
    <div className="space-y-6 bg-background/50 p-6 rounded-lg h-full overflow-y-auto">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gradient-brand">计划详情</h2>

        <Button
          variant="outline"
          size="sm"
          onClick={refetch}
          className="flex items-center gap-2"
        >
          <RotateCcw className="h-4 w-4" />
          刷新数据
        </Button>
      </div>

      {/* 搜索和筛选区域 */}
      <div className="flex items-center bg-background/80 space-x-4 p-4 rounded-lg ">
        <div className="flex items-center space-x-2 flex-1">
          <SearchIcon className="h-4 w-4 " />
          <Input
            placeholder="搜索账号昵称..."
            value={searchTerm}
            onChange={e => setSearchTerm(e.target.value)}
            className="max-w-sm"
          />
        </div>

        <DateRangePicker
          value={dateRange}
          onChange={setDateRange}
        />

        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-32">
            <SelectValue placeholder="发布状态" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">全部状态</SelectItem>
            <SelectItem value="0">待发布</SelectItem>
            <SelectItem value="1">发布成功</SelectItem>
            <SelectItem value="2">发布失败</SelectItem>
            <SelectItem value="3">发布中</SelectItem>
          </SelectContent>
        </Select>

      </div>

      {/* 数据表格 */}
      <div className=" rounded-lg bg-background/80 p-3">
        <DataTable
          columns={columns}
          data={taskList}
          pagination={pagination}
          onPaginationChange={setPagination}
          loading={isLoading}
        />
      </div>
    </div>
  )
}

import { createContext, useContext } from 'react'
import { useSidebar as useUISidebar } from '@/components/ui/sidebar'
import { ResourceType } from '@app/shared/types/resource-cache.types'

// 定义 context 数据的结构
export type SidebarContextType = {
  activePanel: ResourceType // 存储当前活跃的面板名称
  setActivePanel: (panel: ResourceType) => void // 更新活跃面板的函数
  setIsOpen: (open: boolean) => void
}

// 创建 context，初始值为 undefined
export const SidebarContext = createContext<SidebarContextType | undefined>(undefined)

// 自定义 hook 来使用 sidebar context
export const useSidebar = () => {
  const context = useContext(SidebarContext)
  const uiSidebar = useUISidebar()

  if (!context) {
    throw new Error('useSidebar must be used within a SidebarProvider')
  }

  return {
    ...context,
    setIsOpen: uiSidebar.setOpen,
  }
}

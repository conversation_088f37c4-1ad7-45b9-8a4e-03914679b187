import React, { useEffect, useRef } from 'react'
import { Player } from '@remotion/player'
import { useCachedOverlaysContext, useEditorContext } from '@/modules/video-editor/contexts'

import { MainProps, Renderer } from '@clipnest/overlay-renderer'
import { EditableRenderer } from '@/modules/video-editor/components/core/player/editable-renderer'

const RENDER_MODE = import.meta.env.VITE_APP_PLAYER_RENDER_ONLY === 'true'

/**
 * VideoPlayer component that renders a responsive video editor with overlay support
 * The player automatically resizes based on its container and maintains the specified aspect ratio
 */
export const VideoPlayer: React.FC = () => {
  const playerWrapper = useRef<HTMLDivElement>(null)

  const editorContext = useEditorContext()
  const {
    aspectRatio,
    playerDimensions,
    durationInFrames,
    setSelectedOverlay,
    updatePlayerDimensions,
    getAspectRatioDimensions,
    videoPlayer: { playerRef, fps }
  } = editorContext

  const { overlays } = useCachedOverlaysContext()

  const { width: compositionWidth, height: compositionHeight } = getAspectRatioDimensions()

  /**
   * Updates the player dimensions when the container size or aspect ratio changes
   */
  useEffect(() => {
    const handleDimensionUpdate = () => {
      const videoContainer = document.querySelector('.video-container')
      if (!videoContainer) return

      const { width, height } = videoContainer.getBoundingClientRect()
      updatePlayerDimensions(width, height)
    }

    handleDimensionUpdate() // Initial update
    window.addEventListener('resize', handleDimensionUpdate)

    return () => {
      window.removeEventListener('resize', handleDimensionUpdate)
    }
  }, [aspectRatio, updatePlayerDimensions])

  // Constants for player configuration
  const PLAYER_CONFIG = {
    durationInFrames: Math.round(durationInFrames),
    fps
  }

  return (
    <div className="w-full h-full overflow-hidden">
      {/* Grid background container */}
      <div
        onClick={e => {
          if (playerWrapper.current === e.target) {
            setSelectedOverlay(null)
          }
        }}
        className="z-0 video-container relative w-full h-full
        bg-slate-100/90 dark:bg-gray-800
        bg-[linear-gradient(to_right,#80808015_1px,transparent_1px),linear-gradient(to_bottom,#80808015_1px,transparent_1px)]
        dark:bg-[linear-gradient(to_right,#80808010_1px,transparent_1px),linear-gradient(to_bottom,#80808010_1px,transparent_1px)]
        bg-[size:16px_16px]
        shadow-lg"
      >
        {/* Player wrapper with centering */}
        <div
          ref={playerWrapper}
          className="z-10 absolute inset-2 sm:inset-4 flex items-center justify-center"
        >
          <div
            className="relative mx-2 sm:mx-0"
            style={{
              width: Math.min(playerDimensions.width, compositionWidth),
              height: Math.min(playerDimensions.height, compositionHeight),
              maxWidth: '100%',
              maxHeight: '100%'
            }}
          >
            <Player
              ref={playerRef}
              overflowVisible
              className="w-full h-full"
              component={RENDER_MODE ? Renderer : EditableRenderer}
              compositionWidth={compositionWidth}
              compositionHeight={compositionHeight}
              style={{
                width: '100%',
                height: '100%'
              }}
              durationInFrames={PLAYER_CONFIG.durationInFrames}
              fps={PLAYER_CONFIG.fps}
              errorFallback={() => <></>}
              inputProps={{
                overlays,
                playerMetadata: {
                  durationInFrames: PLAYER_CONFIG.durationInFrames,
                  fps: PLAYER_CONFIG.fps,
                  width: compositionWidth,
                  height: compositionHeight
                }
              } satisfies MainProps}
            />
          </div>
        </div>
      </div>
    </div>
  )
}


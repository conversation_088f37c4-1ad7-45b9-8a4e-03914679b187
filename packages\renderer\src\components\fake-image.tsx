import React from 'react'
import { cn } from './lib/utils'

type Props = {
  id: number
  className?: string
  style?: React.CSSProperties
}

function mulberry32(a: number) {
  return function () {
    let t = (a += 0x6d2b79f5)
    t = Math.imul(t ^ (t >>> 15), t | 1)
    t ^= t + Math.imul(t ^ (t >>> 7), t | 61)
    return ((t ^ (t >>> 14)) >>> 0) / 4294967296
  }
}

const cache: Record<number, string> = {}

export const randomGradient = (id: number) => {
  if (cache[id]) return cache[id]
  const rng = mulberry32(id)
  const h1 = rng() * 360
  const h2 = rng() * 360
  const deg = rng() * 360
  const gradient = `linear-gradient(${deg}deg, hsl(${h1}, 70%, 60%), hsl(${h2}, 75%, 65%))`
  cache[id] = gradient
  return gradient
}

export const FakeImage: React.FC<Props> = ({ id, className = '', style }) => {
  const gradient = randomGradient(id)

  return (
    <div
      className={cn(
        'w-full h-full after:content-[attr(div)] after:bg-black after:absolute after:inset-0 after:opacity-0 dark:after:opacity-25',
        className,
      )}
      style={{
        backgroundImage: gradient,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        ...style,
      }}
    />
  )
}

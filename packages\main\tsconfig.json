{"compilerOptions": {"allowSyntheticDefaultImports": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "module": "NodeNext", "target": "ESNext", "sourceMap": false, "moduleResolution": "NodeNext", "skipLibCheck": true, "strict": true, "isolatedModules": true, "types": ["node"], "baseUrl": ".", "paths": {"@app/preload": ["../preload"], "@app/renderer": ["../renderer"], "@app/shared/*": ["../shared/*"], "@/*": ["src/*"]}}, "include": ["src/**/*.ts", "../../types/**/*.d.ts"], "exclude": ["**/*.spec.ts", "**/*.test.ts"]}
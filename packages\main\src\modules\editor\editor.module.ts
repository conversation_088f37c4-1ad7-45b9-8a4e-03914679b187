import { Module } from '@nestjs/common'
import { EditorService } from './editor.service.js'
import { EditorIpcHandler } from './editor.ipc-handler.js'
import { KeyframeExtractorService } from '@/modules/editor/keyframe-extractor.service.js'
import { FileUploaderModule } from '@/modules/file-uploader/file-uploader.module.js'
import { ResourceModule } from '@/modules/resource/resource.module.js'

@Module({
  imports: [FileUploaderModule, ResourceModule],
  providers: [
    EditorService,
    EditorIpcHandler,
    KeyframeExtractorService
  ],
})
export class EditorModule {}

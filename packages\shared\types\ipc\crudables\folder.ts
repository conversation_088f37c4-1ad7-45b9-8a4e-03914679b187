import { CrudableIpcClient } from '../../crudable-ipc-client.js'
import { Folder } from '../../database.types.js'

/**
 * 文件夹IPC客户端接口
 */
export interface FolderIPCClient extends CrudableIpcClient<
  Folder.IFolder,
  Folder.CreateParams,
  Folder.QueryParams,
  Folder.UpdateParams,
  Folder.StatsResult
> {

  /**
   * 获取子文件夹
   * @param props.parentId 父文件夹ID
   * @param props.uid 用户ID
   * @param props.teamId 团队ID
   * @returns 子文件夹列表
   */
  children(props: { parentId: number, uid: string, teamId?: number | null }): Promise<Folder.IFolder[]>

  /**
   * 获取文件夹路径
   * @param id 文件夹ID
   * @returns 路径数组，从根到当前
   */
  path(id: number): Promise<Folder.IFolder[]>

  /**
   * 创建默认文件夹结构
   * @param props.uid 用户ID
   * @param props.teamId 团队ID
   * @returns 创建的根文件夹
   */
  createDefaultStructure(props: { uid: string, teamId?: number | null }): Promise<Folder.IFolder>
}

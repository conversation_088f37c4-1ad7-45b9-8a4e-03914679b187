import { Injectable, Inject } from '@nestjs/common'
import { MaterialFile } from '@app/shared/types/database.types.js'
import { MaterialFileModel } from '@/infra/models/MaterialFileModel.js'
import { MaterialFileRepository } from './material-file.repository.js'
import { CrudableBaseService } from '@/infra/types/CrudableBaseService.js'

/**
 * 素材文件服务
 * 负责处理素材文件相关的业务逻辑
 */
@Injectable()
export class MaterialFileService extends CrudableBaseService<
  MaterialFileModel,
  MaterialFile.CreateParams,
  MaterialFile.UpdateParams,
  MaterialFile.QueryParams,
  MaterialFileRepository
> {

  /**
   * 构造函数
   * @param repository 素材文件仓储实例
   */
  constructor(
    @Inject(MaterialFileRepository)
    repository: MaterialFileRepository
  ) {
    super(repository)
  }

  /**
   * 根据文件哈希查找素材
   * @param hash 文件哈希
   * @returns 素材文件记录或null
   */
  findByHash(hash: string): MaterialFileModel | null {
    return this.repository.findByHash(hash)
  }

  /**
   * 查找用户的素材文件
   * @param uid 用户ID
   * @param folderId 文件夹ID
   * @param teamId 团队ID
   * @returns 素材文件列表
   */
  getUserMaterials(uid: string, folderId?: number, teamId?: number | null): MaterialFileModel[] {
    return this.repository.findUserMaterials(uid, folderId, teamId)
  }

  /**
   * 根据类型查找素材文件
   * @param uid 用户ID
   * @param materialType 素材类型
   * @param teamId 团队ID
   * @returns 素材文件列表
   */
  getMaterialsByType(uid: string, materialType: number, teamId?: number | null): MaterialFileModel[] {
    return this.repository.findByType(uid, materialType, teamId)
  }

  /**
   * 搜索素材文件
   * @param keyword 关键词
   * @param uid 用户ID
   * @param teamId 团队ID
   * @returns 素材文件列表
   */
  searchMaterials(keyword: string, uid: string, teamId?: number | null): MaterialFileModel[] {
    return this.repository.search(keyword, uid, teamId)
  }

  /**
   * 更新素材文件状态
   * @param id 素材文件ID
   * @param status 状态
   * @param reason 原因
   * @returns 是否更新成功
   */
  updateStatus(id: string, status: number, reason?: string): boolean {
    return this.repository.updateStatus(id, reason || '', status)
  }

  /**
   * 批量移动素材文件到指定文件夹
   * @param ids 素材文件ID数组
   * @param folderId 目标文件夹ID
   * @returns 移动的记录数
   */
  batchMoveMaterials(ids: string[], folderId: number): number {
    return this.repository.batchMove(ids, folderId)
  }
}

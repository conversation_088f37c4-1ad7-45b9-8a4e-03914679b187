/**
 * 统一请求状态初始化
 * 在应用启动时调用，确保前后端状态同步
 */

import { ipcRequestStateSync } from './ipc-state-sync'

/**
 * 初始化统一请求状态
 * 应在应用启动时调用
 */
export async function initUnifiedRequestState(): Promise<void> {
  try {
    await ipcRequestStateSync.initialize()
  } catch (error) {
    console.error('[InitUnifiedState] 初始化失败:', error)
    // 不抛出错误，避免影响应用启动
  }
}

/**
 * 检查状态同步健康状况
 * 可用于调试和监控
 */
export async function checkStateSyncHealth(): Promise<{
  isHealthy: boolean
  frontendState: any
  nodeState: any
  issues: string[]
}> {
  const issues: string[] = []
  let frontendState: any = null
  let nodeState: any = null

  try {
    // 获取前端状态
    frontendState = ipcRequestStateSync['getCurrentFrontendState']()
    
    // 获取Node端状态
    nodeState = await ipcRequestStateSync.getNodeState()
    
    // 检查状态一致性
    if (frontendState.auth.accessToken !== nodeState?.auth.accessToken) {
      issues.push('认证token不一致')
    }
    
    if (frontendState.team.tenantId !== nodeState?.team.tenantId) {
      issues.push('团队ID不一致')
    }
    
    // 检查token过期状态
    const isNodeTokenExpired = await ipcRequestStateSync.isNodeTokenExpired()
    if (frontendState.auth.expiresTime && Date.now() >= frontendState.auth.expiresTime) {
      if (!isNodeTokenExpired) {
        issues.push('前端token已过期但Node端未过期')
      }
    }
  } catch (error) {
    issues.push(`状态检查失败: ${error}`)
  }

  return {
    isHealthy: issues.length === 0,
    frontendState,
    nodeState,
    issues
  }
}

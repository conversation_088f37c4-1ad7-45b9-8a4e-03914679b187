import axios, { type AxiosInstance } from 'axios'
import { BASE_URL, TIMEOUT, DEFAULT_HEADERS } from './config'
import { setupRequestInterceptors, setupResponseInterceptors, setupRetryInterceptor } from './interceptors'
import type { RequestConfig } from './types'

/**
 * 创建 Axios 实例
 * @param config 自定义配置
 * @returns Axios 实例
 */
export function createAxiosInstance(config?: RequestConfig): AxiosInstance {
  // 创建 Axios 实例
  const instance = axios.create({
    baseURL: BASE_URL,
    timeout: TIMEOUT,
    headers: DEFAULT_HEADERS,
    // 允许跨域请求携带 cookie
    withCredentials: false,
    // 合并自定义配置
    ...config
  })

  // 设置拦截器
  setupRequestInterceptors(instance)
  setupResponseInterceptors(instance)
  setupRetryInterceptor(instance)

  return instance
}

// 默认 Axios 实例
const axiosInstance = createAxiosInstance()

export default axiosInstance 
import { NumberInput } from '@/components/ui/number-input'
import * as SliderPrimitive from '@radix-ui/react-slider'
import { cn } from '@/components/lib/utils'
import React from 'react'

interface ColorSliderProps {
  value: number
  onChange: (value: number, commit?: boolean) => void
  className?: string
  trackClassName: string
  min?: number
  max?: number
  step?: number
  centerMode?: boolean // 新增：是否使用中心点模式
}
  
// 自定义颜色滑块组件
export const ColorSlider: React.FC<ColorSliderProps> = ({ 
  value, 
  onChange, 
  className = '', 
  trackClassName, 
  min: propMin = 0, 
  max: propMax = 100, 
  step = 1,
  centerMode = false
}) => {
  // 根据centerMode计算实际的min和max
  const min = centerMode ? -propMax : propMin
  const max = propMax
  
  return (
    <div className="flex items-center gap-2 w-full">
      <SliderPrimitive.Root
        value={[value]}
        onValueChange={vals => onChange(vals[0], false)}
        onValueCommit={vals => onChange(vals[0], true)}
        min={min}
        max={max}
        step={step}
        className={cn('relative flex w-full touch-none select-none items-center', className)}
      >
        <SliderPrimitive.Track
          className={cn('relative h-1.5 w-full grow overflow-hidden rounded-full', trackClassName)}
        />
        <SliderPrimitive.Thumb className="block h-4 w-4 rounded-full border border-primary/50 bg-background shadow transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50" />
      </SliderPrimitive.Root>
      <NumberInput
        value={value}
        onChange={val => {
          if (val !== null && val !== undefined) {
            onChange(val, true)
          }
        }}
        min={min}
        max={max}
        step={step}
        className="max-w-16"
      />
    </div>
  )
}
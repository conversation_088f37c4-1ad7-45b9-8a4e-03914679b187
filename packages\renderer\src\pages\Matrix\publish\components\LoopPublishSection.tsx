import React from 'react'
import { <PERSON>, Controller } from 'react-hook-form'
import { CalendarIcon, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Calendar } from '@/components/ui/calendar'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'

interface LoopPublishSectionProps {
  control: Control<any>
}

export const LoopPublishSection: React.FC<LoopPublishSectionProps> = ({
  control
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          循环定时发布
        </CardTitle>
        <CardDescription>
          配置循环发布的日期、时间和频次设置
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 循环发布日期 */}
        <div className="space-y-2">
          <Controller
            name="timeSetting.loopDays"
            control={control}
            render={({ field }) => {
              const selectedDates = field.value || []

              const handleDateSelect = (date: Date | undefined) => {
                if (date) {
                  const timestamp = date.getTime()
                  const currentDates = field.value || []
                  const today = new Date()
                  const maxDate = new Date(today.getTime() + 14 * 24 * 60 * 60 * 1000) // 14天后

                  // 检查日期是否在14天范围内
                  if (date < today || date > maxDate) {
                    alert('只能选择今天起14天内的日期')
                    return
                  }

                  if (currentDates.includes(timestamp)) {
                    // 如果已选择，则移除
                    field.onChange(currentDates.filter(d => d !== timestamp))
                  } else {
                    // 检查是否已选择14天
                    if (currentDates.length >= 14) {
                      alert('最多只能选择14天')
                      return
                    }
                    // 如果未选择，则添加
                    field.onChange([...currentDates, timestamp])
                  }
                }
              }

              return (
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <div className="space-y-2">
                      <Label htmlFor="loopTime" className="flex items-center gap-2">
                        循环时间 <span className="text-red-500">*</span>
                      </Label>
                      <Popover>
                        <PopoverTrigger asChild>
                          <Button
                            variant="outline"
                            className="w-[280px] justify-start text-left font-normal"
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {selectedDates.length > 0 ?
                              `已选择 ${selectedDates.length} 个日期` :
                              <span>请选择发布日期</span>}
                          </Button>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0">
                          <Calendar
                            mode="single"
                            onSelect={handleDateSelect}
                            locale={zhCN}
                            disabled={date => {
                              const today = new Date()
                              const maxDate = new Date(today.getTime() + 14 * 24 * 60 * 60 * 1000)
                              return date < today || date > maxDate
                            }}
                          />
                        </PopoverContent>
                      </Popover>
                    </div>
                    {/* 循环时间 */}
                    <div className="space-y-2">
                      <Label htmlFor="loopTime" className="flex items-center gap-2">
                        循环时间 <span className="text-red-500">*</span>
                      </Label>
                      <Controller
                        name="timeSetting.loopTime"
                        control={control}
                        render={({ field }) => (
                          <Input
                            id="loopTime"
                            type="time"
                            value={field.value || ''}
                            onChange={e => field.onChange(e.target.value)}
                            className="w-[120px]"
                            placeholder="08:00:00"
                          />
                        )}
                      />
                    </div>
                  </div>

                  {/* 已选择的日期列表 */}
                  {selectedDates.length > 0 && (
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm text-muted-foreground">
                        <span>已选择的发布日期</span>
                        <span>{selectedDates.length}/14 天</span>
                      </div>
                      <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto border rounded p-3">
                        {selectedDates.map(timestamp => (
                          <Badge
                            key={timestamp}
                            variant="outline"
                            className="flex items-center gap-1 py-2"
                          >
                            {format(new Date(timestamp), 'yyyy-MM-dd', { locale: zhCN })}
                            <X
                              className="w-3 h-3 cursor-pointer hover:text-red-500"
                              onClick={() => {
                                field.onChange(selectedDates.filter(d => d !== timestamp))
                              }}
                            />
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )
            }}
          />
        </div>

        {/* 每个号每天发布视频频次 */}
        <div className="flex item-center gap-4">
          <Label htmlFor="numEachDay" className="flex items-center gap-2">
            每个号每天发布视频频数
          </Label>
          <div className="flex items-center gap-2">
            <Controller
              name="timeSetting.numEachDay"
              control={control}
              render={({ field }) => (
                <Select
                  value={field.value?.toString() || '1'}
                  onValueChange={value => field.onChange(Number(value))}
                >
                  <SelectTrigger className="w-20">
                    <SelectValue placeholder="1" />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 10 }, (_, i) => i + 1).map(num => (
                      <SelectItem key={num} value={num.toString()}>
                        {num}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
            <span className="text-sm text-muted-foreground">个</span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

import React from 'react'
import { Film } from 'lucide-react'
import { OverlayType } from '@clipnest/remotion-shared/types'
import { registerMaterialPlugin } from '../registry'
import { ResourceType } from '@app/shared/types/resource-cache.types'

const Panel = React.lazy(() =>
  import('@/modules/video-editor/resource-plugin-system/plugin-panels/music.panel')
)

export default registerMaterialPlugin({
  id: ResourceType.MUSIC,
  title: '音乐',
  icon: Film,
  component: Panel,
  overlayType: OverlayType.SOUND,
  order: 20,
})

import { useOverlayEditing } from '@/modules/video-editor/contexts'
import { StoryboardOverlay } from '@clipnest/remotion-shared/types'
import React from 'react'

export const StoryBoardSetting: React.FC = () => {
  const { localOverlay: storyboardOverlay, requestUpdate: _updateOverlay } = useOverlayEditing<StoryboardOverlay>()
  
  return (
    <div>
      {JSON.stringify(storyboardOverlay, null, 2)}
    </div>
  )
}
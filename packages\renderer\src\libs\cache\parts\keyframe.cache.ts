import { SubCacheManager } from '../types'

export interface KeyframeData {
  frames: string[]
  previewFrames: number[]
  durationInFrames: number
  lastUpdated: number
}

export class KeyFrameCacheManager extends SubCacheManager {

  // 关键帧内存缓存，用于提高性能
  private memoryCache: Map<string, KeyframeData> = new Map()

  /**
   * 设置关键帧缓存
   */
  async setKeyframes(videoSrc: string, data: KeyframeData): Promise<void> {
    const keyframeData: KeyframeData = {
      ...data,
      lastUpdated: Date.now()
    }
    await this.store.setItem(videoSrc, keyframeData)

    // 同时更新内存缓存
    this.memoryCache.set(videoSrc, keyframeData)
  }

  /**
   * 获取关键帧缓存
   */
  async getKeyframes(videoSrc: string): Promise<KeyframeData | null> {
    // 先检查内存缓存
    const memoryData = this.memoryCache.get(videoSrc)
    if (memoryData) {
      return memoryData
    }

    // 从 IndexedDB 获取
    const data = await this.store.getItem<KeyframeData>(videoSrc)
    if (data) {
      // 更新内存缓存
      this.memoryCache.set(videoSrc, data)
    }
    return data
  }

  /**
   * 删除特定的关键帧缓存
   */
  async clearKeyframes(videoSrc: string): Promise<void> {
    await this.store.removeItem(videoSrc)
    this.memoryCache.delete(videoSrc)
  }

  /**
   * 清除所有关键帧缓存
   */
  async clearAllKeyframes(): Promise<void> {
    await this.store.clear()
    this.memoryCache.clear()
  }

  async cleanup(now: number, maxAge: number) {
    const keyframeKeysToRemove: string[] = []
    await this.store.iterate((data: KeyframeData, key: string) => {
      if (now - data.lastUpdated > maxAge) {
        keyframeKeysToRemove.push(key)
      }
    })

    for (const key of keyframeKeysToRemove) {
      await this.store.removeItem(key)
      this.memoryCache.delete(key)
    }
  }

  init() {
    void this.store
      .iterate((data: KeyframeData, key: string) => {
        this.memoryCache.set(key, data)
      })
  }
}

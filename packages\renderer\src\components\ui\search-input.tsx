import React from 'react'
import { Search } from 'lucide-react'
import { Input } from '@/components/ui/input' // 使用 react-feather 的 X 图标作为清空按钮
import { cn } from '@/components/lib/utils'
interface SearchInputProps {
  placeholder?: string
  value?: any
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void
  onSearch?: (value: string) => void // 搜索回调
  containerClassName?: string
  style?: React.CSSProperties // 自定义样式
  size?: 'xs' | 'default' | 'lg'
  inputClassName?: string
}

const SearchInput: React.FC<SearchInputProps> = ({
  placeholder = '关键词搜索',
  value = '',
  onChange,
  onSearch,
  containerClassName,
  style = {},
  size = 'default',
  inputClassName = '',
}) => {
  //input样式
  const inputSizeClasses = {
    xs: 'h-6 text-xs pr-8',
    default: 'h-8 text-sm pr-10',
    lg: 'h-10 text-sm pr-10',
  }

  const handleSearch = () => {
    if (onSearch) {
      onSearch(value)
    }
  }

  return (
    <div className={cn('relative mb-2', containerClassName)} style={style}>
      <Input
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        className={cn(inputSizeClasses[size], inputClassName)}
      />
      <Search
        onClick={handleSearch}
        className="absolute right-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground"
      />
    </div>
  )
}

export { SearchInput }

import { Folder } from '@app/shared/types/database.types.js'

/**
 * 文件夹领域模型类
 * 代表文件夹实体及其业务规则
 */
export class FolderModel implements Folder.IFolder {

  /**
     * 文件夹ID
     */
  id: number

  /**
     * 父文件夹ID
     */
  parent_id: number

  /**
     * 删除时间戳（0表示未删除）
     */
  deleted_at: number

  /**
     * 素材类型（1为文件，2为文件夹）
     */
  material_type: number

  /**
     * 文件夹路径
     */
  path: string

  /**
     * 文件夹标题
     */
  title: string

  /**
     * 团队ID
     */
  team_id: number | null

  /**
     * 用户ID
     */
  uid: string

  /**
     * 更新时间戳
     */
  updated_at: number

  /**
     * 创建方式（1手动创建，2系统自动创建）
     */
  auto_create: number

  /**
     * 创建时间戳
     */
  created_at: number

  /**
     * 构造函数
     * @param data 文件夹数据
     */
  constructor(data: Partial<FolderModel> = {}) {
    this.id = data.id ?? 0
    this.parent_id = data.parent_id ?? 0
    this.deleted_at = data.deleted_at ?? 0
    this.material_type = data.material_type ?? 2 // 默认为文件夹类型
    this.path = data.path ?? ''
    this.title = data.title ?? ''
    this.team_id = data.team_id ?? null
    this.uid = data.uid ?? ''
    this.updated_at = data.updated_at ?? Date.now()
    this.auto_create = data.auto_create ?? 1 // 默认为手动创建
    this.created_at = data.created_at ?? Date.now()
  }

  /**
     * 判断是否为根文件夹
     */
  isRoot(): boolean {
    return this.parent_id === 0
  }

  /**
     * 判断是否已删除
     */
  isDeleted(): boolean {
    return this.deleted_at > 0
  }

  /**
     * 判断是否为系统自动创建
     */
  isAutoCreated(): boolean {
    return this.auto_create === 2
  }

  /**
     * 判断是否属于指定用户
     * @param uid 用户ID
     */
  belongsToUser(uid: string): boolean {
    return this.uid === uid
  }

  /**
     * 判断是否属于指定团队
     * @param teamId 团队ID
     */
  belongsToTeam(teamId: number): boolean {
    return this.team_id === teamId
  }

  /**
     * 获取文件夹的显示名称
     */
  getDisplayName(): string {
    return this.title || '未命名文件夹'
  }

  /**
     * 将文件夹转换为JSON对象
     */
  toJSON(): Record<string, any> {
    return {
      id: this.id,
      parent_id: this.parent_id,
      deleted_at: this.deleted_at,
      material_type: this.material_type,
      path: this.path,
      title: this.title,
      team_id: this.team_id,
      uid: this.uid,
      updated_at: this.updated_at,
      auto_create: this.auto_create,
      created_at: this.created_at,
    }
  }
}

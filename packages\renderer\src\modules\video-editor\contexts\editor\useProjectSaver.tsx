import React, { use<PERSON>allback, useEffect, useRef, useState } from 'react'
import { AUTO_SAVE_INTERVAL } from '@/modules/video-editor/constants'
import { AutosaveStatus } from '@/modules/video-editor/components/autosave/autosave-status'
import { AutosaveRecoveryDialog } from '@/modules/video-editor/components/autosave/autosave-recovery-dialog'
import { isEqual } from 'lodash'
import { EditorState, hasAutosave, loadProjectState, saveProjectState } from '@/modules/video-editor/utils/project-state-storage'

export interface AutosaveOptions {
  /**
   * Interval in milliseconds to exec auto-saving
   * @default 5000 (5 seconds)
   */
  interval?: number

  /**
   * Function to call when an autosave is loaded
   */
  onLoad?: (data: EditorState) => void

  /**
   * Function to call when an autosave is saved
   */
  onSave?: () => void

  /**
   * Function to call when an autosave is detected on initial load
   */
  onAutosaveDetected?: (timestamp: number) => void

  /**
   * Function to call when cloud load starts
   */
  onCloudLoadStart?: () => void

  /**
   * Function to call when cloud load succeeds
   */
  onCloudLoadSuccess?: () => void

  /**
   * Function to call when cloud load fails
   */
  onCloudLoadError?: (error: Error) => void
}

/**
 * Hook for automatically saving editor state to IndexedDB
 *
 * @param projectId Unique identifier for the project
 * @param state Current state to be saved
 * @param options Configuration options for autosave behavior
 * @returns Object with functions to manually save and load state
 */
function useAutosave(
  projectId: string,
  state: EditorState,
  options: AutosaveOptions = {},
) {
  const {
    interval = 5000,
    onLoad,
    onSave,
    onAutosaveDetected,
    onCloudLoadStart,
    onCloudLoadSuccess,
    onCloudLoadError
  } = options

  const timerRef = useRef<NodeJS.Timeout | null>(null)
  const lastSavedStateRef = useRef<string>('')
  const autoSaveCheckedRef = useRef(false)

  // Check for existing autosave on mount, but only once
  useEffect(() => {
    const checkForAutosave = async () => {
      if (autoSaveCheckedRef.current) return

      try {
        // 开始云端加载
        if (onCloudLoadStart) onCloudLoadStart()

        const stateFromCloud = await loadProjectState(projectId, true)

        // 云端加载成功
        onCloudLoadSuccess?.()

        if (stateFromCloud) {
          onLoad?.(stateFromCloud)
        }

        const autoSaved = await hasAutosave(projectId)

        if (autoSaved && onAutosaveDetected) {
          const sameAsManual = stateFromCloud && isEqual(stateFromCloud.tracks, autoSaved.editorState.tracks)

          if (!sameAsManual) {
            onAutosaveDetected(autoSaved.timestamp)
          }
        }
      } catch (error) {
        console.error('Failed to check for autosave:', error)

        // 云端加载失败
        if (onCloudLoadError) onCloudLoadError(error)
      } finally {
        autoSaveCheckedRef.current = true
      }
    }

    void checkForAutosave()
  }, [projectId, onCloudLoadStart, onCloudLoadSuccess, onCloudLoadError, onAutosaveDetected])

  // Set up autosave timer
  useEffect(() => {
    // Don't start autosave if projectId is not valid
    if (!projectId) return

    const saveIfChanged = async () => {
      const currentStateString = JSON.stringify(state)

      // Only save if state has changed since last save
      if (currentStateString !== lastSavedStateRef.current) {
        try {
          await saveProjectState(projectId, state)
          lastSavedStateRef.current = currentStateString
          if (onSave) onSave()
        } catch (error) {
          console.error('Autosave failed:', error)
        }
      }
    }

    // Set up interval for autosave
    timerRef.current = setInterval(saveIfChanged, interval)

    // Clean up timer on unmount
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
        timerRef.current = null
      }
    }
  }, [projectId, state, interval, onSave])

  // Function to manually save state
  const saveState = useCallback(async () => {
    try {
      await saveProjectState(projectId, state, true)
      lastSavedStateRef.current = JSON.stringify(state)
      if (onSave) onSave()
      return true
    } catch (error) {
      console.error('Manual save failed:', error)
      return false
    }
  }, [state])

  // Function to manually load state
  const loadState = async () => {
    try {
      const loadedState = await loadProjectState(projectId)
      if (loadedState && onLoad) {
        onLoad(loadedState)
      }
      return loadedState
    } catch (error) {
      console.error('Load failed:', error)
      return null
    }
  }

  return {
    saveState,
    loadState,
  }
}

export const useProjectSaver = (
  projectId: string,
  initialLoadComplete: boolean,
  editorState: EditorState,
  options: Pick<AutosaveOptions, 'onLoad' | 'onCloudLoadStart' | 'onCloudLoadSuccess' | 'onCloudLoadError'>
) => {
  // Autosave state
  const [showRecoveryDialog, setShowRecoveryDialog] = useState(false)
  const [autosaveTimestamp, setAutosaveTimestamp] = useState<number | null>(
    null,
  )
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaveTime, setLastSaveTime] = useState<number | null>(null)

  // Implement load state
  const { saveState, loadState } = useAutosave(projectId, editorState, {
    interval: AUTO_SAVE_INTERVAL,
    onSave: () => {
      setIsSaving(false)
      setLastSaveTime(Date.now())
    },
    onLoad: options.onLoad,
    onAutosaveDetected: timestamp => {
      // console.log(`autosave detected: ${timestamp}, initialized: ${initialLoadComplete}`)
      // Only show recovery dialog on initial load, not during an active session
      if (!initialLoadComplete) {
        setAutosaveTimestamp(timestamp)
        setShowRecoveryDialog(true)
      }
    },
  })

  // console.debug('DEBUG: tracks', tracks)
  // Handle recovery dialog actions
  const handleRecoverAutosave = async () => {
    const loadedState = await loadState()
    console.debug('loadedState', loadedState)
    setShowRecoveryDialog(false)
  }

  const handleDiscardAutosave = () => {
    setShowRecoveryDialog(false)
  }

  // Manual save function for use in keyboard shortcuts or save button
  const saveProject = useCallback(async () => {
    setIsSaving(true)
    await saveState()
  }, [saveState])

  // Set up keyboard shortcut for manual save (Ctrl+S)
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault()
        saveProject()
      }
    }

    window.addEventListener('keydown', handleKeyDown)
    return () => window.removeEventListener('keydown', handleKeyDown)
  }, [saveProject])

  const ProjectSaver = useCallback(
    () => (
      <>
        {/* Autosave Status Indicator */}
        <AutosaveStatus
          isSaving={isSaving}
          lastSaveTime={lastSaveTime}
        />

        {autosaveTimestamp && (
          <AutosaveRecoveryDialog
            open={showRecoveryDialog}
            projectId={projectId}
            timestamp={autosaveTimestamp}
            onRecover={handleRecoverAutosave}
            onDiscard={handleDiscardAutosave}
            onClose={() => setShowRecoveryDialog(false)}
          />
        )}
      </>
    ),
    [isSaving, lastSaveTime, showRecoveryDialog, autosaveTimestamp, projectId]
  )

  return {
    saveProject,
    ProjectSaver
  }
}

import { useCallback } from 'react'
import { OverlayType, SoundOverlay } from '@clipnest/remotion-shared/types'
import { cacheManager } from '@/libs/cache/cache-manager'
import { useOverlayHelper } from './helpers/useOverlayHelper'
import { SoundResource } from '@/types/resources'
import { ResourceType } from '@app/shared/types/resource-cache.types'

export function useAddAudioToTimeline() {
  const { addOverlayToGlobalTrack } = useOverlayHelper()

  const handleAddAudioToTimeline = useCallback(
    async (resource: SoundResource.SoundLocal) => {
      const getSrcPath = (audioUrl: string) => {
        let srcPath = audioUrl

        const localPath = cacheManager.resource.getResourcePathSync(ResourceType.SOUND, audioUrl)

        if (localPath) {
          srcPath = localPath
        } else {
          srcPath = audioUrl
        }

        return srcPath
      }

      try {
        const musicDurationInFrames = Math.round((resource.content.durationMsec / 1000) * 30)
        const src = getSrcPath(resource.content.itemUrl)
        const overlay: SoundOverlay = {
          id: Date.now(),
          type: OverlayType.SOUND,
          content: resource.title,
          src: src,
          localSrc: src,
          durationInFrames: musicDurationInFrames,
          from: 0,
          height: 100,
          width: 200,
          left: 0,
          top: 0,
          isDragging: false,
          rotation: 0,
          styles: {
            volume: 1,
          },
        }

        addOverlayToGlobalTrack(overlay)
        console.log('添加音乐到时间轴')
      } catch (error) {
        console.error('添加音乐到时间轴失败:', error)
      }
    },
    [addOverlayToGlobalTrack],
  )

  return { handleAddAudioToTimeline }
}

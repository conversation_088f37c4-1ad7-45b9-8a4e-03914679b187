import { Compose } from '../../database.types.js'
import { CrudableIpcClient } from '../../crudable-ipc-client.js'

/**
 * 合成记录IPC客户端接口
 */
export interface ComposeIPCClient extends CrudableIpcClient<
  Compose.ICompose,
  Compose.CreateParams,
  Compose.QueryParams,
  Compose.UpdateParams,
  Compose.StatsResult
> {

  /**
   * 获取团队合成记录
   * @param data 参数对象
   * @returns 合成记录列表
   */
  teamComposes(data: { teamId: number, status?: number }): Promise<Compose.ICompose[]>;

  /**
   * 获取脚本相关的合成记录
   * @param data 参数对象
   * @returns 合成记录列表
   */
  scriptComposes(data: { scriptId: string, teamId?: number }): Promise<Compose.ICompose[]>;

  /**
   * 搜索合成记录
   * @param data 参数对象
   * @returns 合成记录列表
   */
  searchCompose(data: { keyword: string, teamId?: number }): Promise<Compose.ICompose[]>;

  /**
   * 更新下载状态
   * @param data 参数对象
   * @returns 是否更新成功
   */
  updateDownloadStatus(data: { id: number, downloadStatus: number, progress?: number, reason?: string }): Promise<boolean>;

  /**
   * 标记为已读
   * @param id 合成记录ID
   * @returns 是否更新成功
   */
  markAsRead(id: number): Promise<boolean>;

  /**
   * 获取合成记录统计数据
   * @param data 参数对象
   * @returns 统计结果
   */
  getComposeStats(data: { teamId?: number }): Promise<Compose.StatsResult>;
}

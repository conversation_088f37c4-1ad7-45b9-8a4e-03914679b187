import React, { useRef, useEffect, useState } from 'react'

interface KeepAliveProps {
  active: boolean
  children: React.ReactNode
  name: string
}

/**
 * 简单的 KeepAlive 组件，用于缓存页面状态
 * 当 active 为 false 时，组件会被隐藏但不会被销毁
 */
const KeepAlive: React.FC<KeepAliveProps> = ({ active, children, name }) => {
  const [cached, setCached] = useState<React.ReactNode | null>(null)
  const firstRender = useRef(true)

  // 第一次渲染或者 children 变化时更新缓存
  useEffect(() => {
    if (firstRender.current || active) {
      firstRender.current = false
      setCached(children)
    }
  }, [children, active])

  return (
    <div
      data-keepalive-name={name}
      style={{
        display: active ? 'block' : 'none',
        height: '100%',
        width: '100%',
        overflow: 'hidden',
      }}
    >
      {cached}
    </div>
  )
}

export default KeepAlive

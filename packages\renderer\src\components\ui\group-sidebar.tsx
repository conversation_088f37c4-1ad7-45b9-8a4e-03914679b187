import React, { useState, useRef } from 'react'
import { ChevronDownIcon, ChevronRightIcon, PlusIcon, EditIcon, TrashIcon, MoreHorizontalIcon } from 'lucide-react'
import { cn } from '@/components/lib/utils'
import { Button } from './button'
import { Input } from './input'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from './collapsible'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from './dropdown-menu'
import { MatrixModule } from '@/libs/request/api/matrix'
import useRequest from '@/hooks/useRequest'
import { GroupDialog, GroupDialogRef } from '@/pages/Matrix/account/components/GroupDialog'
import { queryClient } from '@/main'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { GroupItem } from '@/types/matrix/douyin'

interface GroupSidebarProps {
  groups: GroupItem[]
  selectedGroupId?: number
  onGroupSelect?: (groupId: number) => void
  className?: string
  searchPlaceholder?: string
  showOptions?: boolean
}

export function GroupSidebar({
  groups,
  selectedGroupId,
  onGroupSelect,
  className,
  searchPlaceholder = '搜索分组',
  showOptions = true
}: GroupSidebarProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [expandedGroups, setExpandedGroups] = useState<Set<number>>(new Set())
  const [hasInitialized, setHasInitialized] = useState(false)
  const groupDialogRef = useRef<GroupDialogRef>(null)

  // 递归检查分组是否存在
  const findGroupById = (groups: GroupItem[], targetId: number): boolean => {
    return groups.some(group => {
      if (group.id === targetId) return true
      if (group.children && group.children.length > 0) {
        return findGroupById(group.children, targetId)
      }
      return false
    })
  }

  // 当 groups 数据存在时，默认选中第一个分组
  React.useEffect(() => {
    if (groups && groups.length > 0 && onGroupSelect && !hasInitialized) {
      // 如果没有选中任何分组，或者选中的分组不存在于当前分组列表中，则选中第一个分组
      const hasValidSelection = selectedGroupId && findGroupById(groups, selectedGroupId)

      if (!hasValidSelection) {
        onGroupSelect(groups[0].id)
      }

      setHasInitialized(true)
    }
  }, [groups, selectedGroupId, onGroupSelect, hasInitialized])

  const handleAddGroup = () => {
    groupDialogRef.current?.openCreateDialog()
  }

  const handleAddChildrenGroup = (parentId: number) => {
    groupDialogRef.current?.openCreateDialog(parentId)
  }

  const handleEditGroup = (group: GroupItem) => {
    groupDialogRef.current?.openEditDialog(group)
  }

  const toggleGroup = (groupId: number) => {
    const newExpanded = new Set(expandedGroups)
    if (newExpanded.has(groupId)) {
      newExpanded.delete(groupId)
    } else {
      newExpanded.add(groupId)
    }
    setExpandedGroups(newExpanded)
  }

  const filteredGroups = React.useMemo(() => {
    if (!searchTerm) return groups

    const filterGroups = (items: GroupItem[]): GroupItem[] => {
      return items.filter(item => {
        const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase())
        const hasMatchingChildren = item.children.length > 0 && filterGroups(item.children).length > 0
        return matchesSearch || hasMatchingChildren
      }).map(item => ({
        ...item,
        children: filterGroups(item.children)
      }))
    }

    return filterGroups(groups)
  }, [groups, searchTerm])

  const { mutate: deleteGroup } = useRequest(
    (id: number) => MatrixModule.deleteGroup(id),
    {
      actionName: '删除分组',
      onSuccess: () => {
        queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.ACCOUNT_GROUP] })
      }
    })

  const renderGroupItem = (group: GroupItem, level = 0) => {
    const hasChildren = group.children.length > 0
    const isExpanded = expandedGroups.has(group.id)
    const isSelected = selectedGroupId === group.id

    return (
      <div key={group.id} className="w-full">
        <Collapsible open={isExpanded} onOpenChange={() => toggleGroup(group.id)}>
          <div
            onClick={() => onGroupSelect?.(group.id)}

            className={cn(
              'flex items-center  cursor-pointer justify-between w-full px-2 py-1.5 text-sm rounded-md hover:bg-accent hover:text-accent-foreground group',
              isSelected && 'bg-accent text-accent-foreground',
              level > 0 && 'ml-3'
            )}
          >
            <div className="flex items-center flex-1 min-w-0">
              {hasChildren ? (
                <CollapsibleTrigger asChild>
                  <div  className="aspect-square w-4 flex items-center justify-center rounded-full p-0 mr-1 hover:bg-gray-400/50">
                    {isExpanded ? (
                      <ChevronDownIcon className="h-3 w-3" />
                    ) : (
                      <ChevronRightIcon className="h-3 w-3" />
                    )}
                  </div>
                </CollapsibleTrigger>
              ) : (
                <div className="w-5" />
              )}

              <div
                className="flex items-center justify-between flex-1 min-w-0 text-left"
              >
                <span className="truncate">{group.name}</span>

              </div>
            </div>
            {
              showOptions &&
                (

                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 data-[state=open]:opacity-100"
                      >
                        <MoreHorizontalIcon className="h-3 w-3" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => handleAddChildrenGroup(group.id)}>
                        <PlusIcon className="h-4 w-4 mr-2" />
                        添加子分组
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEditGroup(group)}>
                        <EditIcon className="h-4 w-4 mr-2" />
                        编辑
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => deleteGroup(group.id)}
                        className="text-destructive"
                      >
                        <TrashIcon className="h-4 w-4 mr-2" />
                        删除
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                )
            }
          </div>

          {hasChildren && (
            <CollapsibleContent className="flex flex-col gap-2 mt-2">
              {group.children.map(child => renderGroupItem(child, level + 1))}
            </CollapsibleContent>
          )}
        </Collapsible>
      </div>
    )
  }

  return (
    <div className={cn('flex flex-col h-full', className)}>
      {/* 头部 */}
      <div className="p-4 border-b">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-lg font-semibold">分组</h2>
          {
            showOptions && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleAddGroup}
                className="h-8 w-8 p-0"
              >
                <PlusIcon className="h-4 w-4" />
              </Button>
            )
          }
        </div>

        {/* 搜索框 */}
        <Input
          placeholder={searchPlaceholder}
          value={searchTerm}
          onChange={e => setSearchTerm(e.target.value)}
          className="h-8"
        />
      </div>

      {/* 分组列表 */}
      <div className="flex-1 overflow-y-auto p-2">
        <div className="flex flex-col gap-2">
          {filteredGroups.map(group => renderGroupItem(group))}
        </div>

        {filteredGroups.length === 0 && (
          <div className="text-center text-muted-foreground py-8">
            {searchTerm ? '未找到匹配的分组' : '暂无分组'}
          </div>
        )}
      </div>

      <GroupDialog
        ref={groupDialogRef}
      />
    </div>
  )
}

import { Injectable, Inject } from '@nestjs/common'
import { Task } from '@app/shared/types/database.types.js'
import { TaskModel } from '@/infra/models/TaskModel.js'
import { TaskRepository } from './task.repository.js'
import { CrudableBaseService } from '@/infra/types/CrudableBaseService.js'

/**
 * 任务服务类
 */
@Injectable()
export class TaskService extends CrudableBaseService<
  TaskModel,
  Task.CreateParams,
  Task.UpdateParams,
  Task.QueryParams,
  TaskRepository
> {

  /**
   * 构造函数
   * @param repository 任务仓储实例
   */
  constructor(
    @Inject(TaskRepository)
    repository: TaskRepository
  ) {
    super(repository)
  }

  /**
   * 获取用户任务
   * @param uid 用户ID
   * @param status 状态过滤
   * @param teamId 团队ID
   * @returns 任务列表
   */
  getUserTasks(uid: string, status?: number, teamId?: number | null): TaskModel[] {
    try {
      return this.repository.findUserTasks(uid, status, teamId)
    } catch (error: any) {
      throw new Error(`获取用户任务失败: ${error.message}`)
    }
  }

  /**
   * 获取文件夹下的任务
   * @param folderId 文件夹ID
   * @param uid 用户ID
   * @param teamId 团队ID
   * @returns 任务列表
   */
  getTasksByFolder(folderId: string, uid: string, teamId?: number | null): TaskModel[] {
    try {
      return this.repository.findByFolder(folderId, uid, teamId)
    } catch (error: any) {
      throw new Error(`获取文件夹任务失败: ${error.message}`)
    }
  }

  /**
   * 根据类型获取任务
   * @param uid 用户ID
   * @param type 任务类型
   * @param teamId 团队ID
   * @returns 任务列表
   */
  getTasksByType(uid: string, type: number, teamId?: number | null): TaskModel[] {
    try {
      return this.repository.findByType(uid, type, teamId)
    } catch (error: any) {
      throw new Error(`获取类型任务失败: ${error.message}`)
    }
  }

  /**
   * 搜索任务
   * @param keyword 关键词
   * @param uid 用户ID
   * @param teamId 团队ID
   * @returns 任务列表
   */
  searchTasks(keyword: string, uid: string, teamId?: number | null): TaskModel[] {
    try {
      return this.repository.search(keyword, uid, teamId)
    } catch (error: any) {
      throw new Error(`搜索任务失败: ${error.message}`)
    }
  }

  /**
   * 更新任务状态
   * @param id 任务ID
   * @param status 状态
   * @param progress 进度
   * @param reason 失败原因
   * @returns 是否更新成功
   */
  updateTaskStatus(id: number, status: number, progress?: number, reason?: string): boolean {
    try {
      return this.repository.updateStatus(id, reason || '', status, { progress })
    } catch (error: any) {
      throw new Error(`更新任务状态失败: ${error.message}`)
    }
  }

  /**
   * 批量移动任务到指定文件夹
   * @param ids 任务ID数组
   * @param folderId 目标文件夹ID
   * @returns 移动的记录数
   */
  batchMoveTasks(ids: string[], folderId: string): number {
    try {
      return this.repository.batchMove(ids, folderId)
    } catch (error: any) {
      throw new Error(`批量移动任务失败: ${error.message}`)
    }
  }

  /**
   * 获取任务统计数据
   * @param uid 用户ID
   * @param teamId 团队ID
   * @returns 统计数据
   */
  getTaskStats(uid: string, teamId?: number | null): any {
    try {
      return this.repository.getTaskStats(uid, teamId)
    } catch (error: any) {
      throw new Error(`获取任务统计数据失败: ${error.message}`)
    }
  }
}

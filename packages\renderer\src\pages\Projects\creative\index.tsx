import React, { useMemo } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, Edit3, <PERSON><PERSON><PERSON>, PlaySquare, Plus, Save, Scissors, Trash } from 'lucide-react'
import { Link, useParams } from 'react-router'
import { useInfiniteQueryScriptList } from '@/hooks/queries/useQueryScript'
import { Script } from '@/types/project'
import { FakeImage } from '@/components/fake-image'
import { useCreateScript, useDuplicateScript, useUpdateScript } from './modals'
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuList,
} from '@/components/ui/navigation-menu'
import { NavigationMenuTrigger } from '@radix-ui/react-navigation-menu'
import { Separator } from '@/components/ui/separator'
import { useDeleteModal } from '@/components/modal/delete'
import { ResourceModule } from '@/libs/request/api/resource'
import { useQueryClient } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import useVirtualTabsStore from '@/libs/stores/useVirtualTabsStore'
import { useProjectsContext } from '../context'
import { useIntersectionObserver } from '@/hooks/useIntersectionObserver'

function CreativeCard({ script }: { script: Script }) {
  const updateScript = useUpdateScript()
  const duplicateScript = useDuplicateScript()
  const deleteModal = useDeleteModal()
  const queryClient = useQueryClient()
  const { pushNamedTab } = useVirtualTabsStore()

  const handleGoToEditor = () => {
    pushNamedTab('Editor', {
      id: script.id.toString(),
      projectId: script.projectId.toString(),
    })
  }

  return (
    <div className="aspect-[3/4] flex flex-col rounded-lg shadow-sm border hover:shadow-md transition-all">
      <div className="group relative flex-1 w-full bg-muted rounded-t-lg overflow-hidden group aspect-auto">
        {script.coverContent ? (
          <img src={script.coverContent} alt={script.title} className="h-full w-full object-cover" draggable="false" />
        ) : (
          <FakeImage id={script.id} />
        )}

        <div className="opacity-0 group-hover:opacity-100 flex flex-col items-center justify-center gap-4 rounded-t-lg absolute inset-0 bg-black/40 transition-all">
          <Button
            size="lg"
            variant="secondary"
            onClick={() => {
              pushNamedTab('Script', {
                id: script.id.toString(),
              })
            }}
          >
            去编辑脚本
          </Button>
          <Button size="lg" variant="default" onClick={handleGoToEditor}>
            去剪辑视频
          </Button>
        </div>
      </div>
      <div className="p-2 w-full min-w-0 flex items-center justify-between">
        <div className="flex flex-col gap-1 text-sm items-start grow min-w-0">
          <span className="truncate w-full" title={script.title}>
            {script.title}
          </span>
          <span className="border border-primary leading-none p-0.5 rounded">批量混剪</span>
        </div>
        <NavigationMenu>
          <NavigationMenuList>
            <NavigationMenuItem>
              <NavigationMenuTrigger asChild>
                <Button variant="ghost" size="icon" className="shrink-0">
                  <Ellipsis className="size-4" />
                </Button>
              </NavigationMenuTrigger>
              <NavigationMenuContent className="p-1">
                <div className="flex flex-col gap-1 min-w-36 *:flex *:gap-2 *:items-center *:justify-start">
                  <Button variant="ghost" onClick={() => updateScript(script.id, script.title)}>
                    <Edit3 className="size-4" />
                    重命名
                  </Button>
                  <Button variant="ghost" onClick={() => duplicateScript(script.id, script.title, script.projectId)}>
                    <Copy className="size-4" />
                    创建副本
                  </Button>
                  <Button variant="ghost" asChild>
                    <Link to={`../works?scriptId=${script.id}`} relative="path">
                      <PlaySquare className="size-4" />
                      我的成片
                    </Link>
                  </Button>
                  <Button variant="ghost">
                    <Scissors className="size-4 rotate-90" />
                    生成混剪视频
                  </Button>
                  <Button variant="ghost">
                    <Save className="size-4" />
                    查看保存的视频
                  </Button>
                  <Separator orientation="horizontal" />
                  <Button
                    variant="ghost"
                    className="hover:bg-destructive/20"
                    onClick={() => {
                      return deleteModal({
                        kind: '脚本',
                        name: script.title,
                        danger: true,
                        action: async () => {
                          await ResourceModule.script.remove({ ids: [script.id] })
                          await queryClient.invalidateQueries({
                            queryKey: [QUERY_KEYS.SCRIPT_LIST],
                          })
                        },
                      })
                    }}
                  >
                    <Trash className="size-4" />
                    删除
                  </Button>
                </div>
              </NavigationMenuContent>
            </NavigationMenuItem>
          </NavigationMenuList>
        </NavigationMenu>
      </div>
    </div>
  )
}

export default function Creatives() {
  const { keyword } = useProjectsContext()
  const createScript = useCreateScript()
  const params = useParams()
  const { data, fetchNextPage } = useInfiniteQueryScriptList({ projectId: params.projectId, keyword })
  const scrollRef = React.useRef<HTMLDivElement>(null)
  const NextPageFetcher = useIntersectionObserver({
    onIntersect: () => fetchNextPage(),
    deps: [data],
    root: scrollRef.current,
  })
  const creatives = useMemo(() => data?.pages.flatMap(v => v.list) || [], [data])

  return (
    <>
      <div className="flex flex-col flex-1 overflow-auto relative" ref={scrollRef}>
        <div className="flex sticky top-0 z-10 p-4 bg-main">
          <Button
            className="bg-gradient-brand hover:bg-transparent hover:opacity-90 text-neutral-900"
            onClick={() => createScript(Number(params.projectId))}
          >
            <Plus className="w-4 h-4 mr-2" />
            创建混剪创意
          </Button>
        </div>
        {creatives.length ? (
          <>
            <div className="grid gap-4 grid-cols-[repeat(auto-fill,minmax(12rem,1fr))] px-4 pb-4">
              {creatives.map(creative => (
                <CreativeCard key={creative.id} script={creative} />
              ))}
            </div>
            <NextPageFetcher />
          </>
        ) : (
          <div className="flex flex-col items-center justify-center h-64 text-muted-foreground">
            <div className="text-lg mb-2">暂无创意</div>
            <div className="text-sm">请先创建一个创意</div>
          </div>
        )}
      </div>
    </>
  )
}

export type Combo = {
  combination: number[]
  similarity: number
  max?: number
}

export type RenderRequestPayload = {
  id: string
  inputProps: {
    overlays: any[],
    playerMetadata: {
      width: number
      height: number
      fps: number
      durationInFrames: number
    }
  }
}

/**
 * 编辑器IPC客户端接口
 */
export interface EditorIPCClient {

  /**
   *
   * @param props.limit 生成数量上限
   * @param props.threshold 生成结果的相似度阈值
   * @param props.matrix 每一列中可用的行序号
   */
  generateCombos(props: { limit: number, threshold: number, matrix: number[][] }): Promise<Combo[]>

  extractVideoKeyFrames(props: {
    src: string
  }): Promise<{ frameNumber: number, dataUrl: string }[]>

  /**
   * 压缩编辑器状态数据
   * @param editorState 编辑器状态对象
   * @returns Promise<Buffer> 压缩后的二进制数据
   */
  compressEditorState(editorState: any): Promise<Buffer>

  /**
   * 解压编辑器状态数据
   * @param compressedData 压缩的二进制数据
   * @returns Promise<any> 解压后的编辑器状态对象
   */
  decompressEditorState(compressedData: any): Promise<any>

  /**
   * 上传混剪结果到服务器
   * @param params 上传参数
   * @returns Promise<any> 上传结果
   */
  uploadMixcutResult(params: {
    scriptId: string
    data: RenderRequestPayload
    similarity: number
    cover?: string
    duration?: number
  }): Promise<any>

  /**
   * 获取音频文件的时长
   * @param audioUrl 音频文件URL
   * @returns Promise<{duration: number, localUrl: string}> 音频时长（秒）和本地文件URL
   */
  getAudioDuration(audioUrl: string): Promise<number>
}

import { useCallback, useState } from 'react'

export type TimelineTrackDnDHook = {
  timelineTrackDndHandlers: {
    onRowDragStart: (trackIndex: number) => void,
    onRowDragOver: (trackIndex: number) => void,
    onRowDrop: () => void,
    onRowDragEnd: () => void,
  },
  draggedRowIndex: number | null
  dragOverRowIndex: number | null
}

export const useTimelineTrackDnD = (): TimelineTrackDnDHook => {
  // Add state for row dragging
  const [draggedRowIndex, setDraggedRowIndex] = useState<number | null>(null)
  const [dragOverRowIndex, setDragOverRowIndex] = useState<number | null>(null)

  const handlers = {
    onRowDragStart: (trackIndex: number) => {
      setDraggedRowIndex(trackIndex)
    },
    onRowDragOver: (trackIndex: number) => {
      if (draggedRowIndex === null) return
      setDragOverRowIndex(trackIndex)
    },
    onRowDrop: useCallback(() => {
      if (draggedRowIndex === null) return
      setDraggedRowIndex(null)
      setDragOverRowIndex(null)
    }, [draggedRowIndex]),
    onRowDragEnd: () => {
      setDraggedRowIndex(null)
      setDragOverRowIndex(null)
    }
  }

  return {
    timelineTrackDndHandlers: handlers,
    draggedRowIndex,
    dragOverRowIndex,
    // setDraggedRowIndex,
    // setDragOverRowIndex,
  }
}

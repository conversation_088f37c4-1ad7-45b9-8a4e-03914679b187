import React, { forwardRef, useImperative<PERSON><PERSON>le, useState, useMemo } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogFooter,
} from '@/components/ui/dialog'
import { MatrixModule } from '@/libs/request/api/matrix'
import { useRequest } from '@/hooks/useRequest'
import { queryClient } from '@/main'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { useQueryAccountGroup } from '@/hooks/queries/useQueryAccountGroup'
import { ChevronDown, ChevronRight } from 'lucide-react'
import { cn } from '@/components/lib/utils'
import { Account, GroupItem } from '@/types/matrix/douyin'

export interface AssignAccountGroupDialogRef {
  open: (account: Account) => void
}

interface AssignAccountGroupDialogProps {
  onSuccess?: () => void
}

// 树型节点组件
interface TreeNodeProps {
  group: GroupItem
  selectedIds: number[]
  expandedIds: Set<number>
  onToggleExpand: (id: number) => void
  onToggleSelect: (id: number, checked: boolean) => void
  searchTerm: string
  level?: number
}

const TreeNode: React.FC<TreeNodeProps> = ({
  group,
  selectedIds,
  expandedIds,
  onToggleExpand,
  onToggleSelect,
  searchTerm,
  level = 0
}) => {
  const hasChildren = group.children && group.children.length > 0
  const isExpanded = expandedIds.has(group.id)
  const isSelected = selectedIds.includes(group.id)

  // 检查是否匹配搜索条件
  const matchesSearch = !searchTerm || group.name.toLowerCase().includes(searchTerm.toLowerCase())

  // 检查子节点是否有匹配的
  const hasMatchingChildren = useMemo(() => {
    if (!searchTerm || !hasChildren) return false

    const checkChildren = (children: GroupItem[]): boolean => {
      return children.some(child =>
        child.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (child.children && checkChildren(child.children))
      )
    }

    return checkChildren(group.children)
  }, [group.children, searchTerm, hasChildren])

  // 如果不匹配搜索且子节点也不匹配，则不显示
  if (searchTerm && !matchesSearch && !hasMatchingChildren) {
    return null
  }

  return (
    <div className="select-none">
      <div
        className={cn(
          'flex items-center py-1 px-2 hover:bg-gray-50/10 rounded',
          level > 0 && 'ml-4'
        )}
        style={{ paddingLeft: `${level * 16 + 8}px` }}
      >
        {/* 展开/收起按钮 */}
        {hasChildren && (
          <button
            onClick={() => onToggleExpand(group.id)}
            className="mr-1 p-0.5 hover:bg-gray-200/10 rounded"
          >
            {isExpanded ? (
              <ChevronDown className="w-4 h-4" />
            ) : (
              <ChevronRight className="w-4 h-4" />
            )}
          </button>
        )}

        {/* 如果没有子节点，添加占位空间 */}
        {!hasChildren && <div className="w-5 mr-1" />}

        {/* 复选框 */}
        <Checkbox
          id={`group-${group.id}`}
          checked={isSelected}
          onCheckedChange={checked => onToggleSelect(group.id, !!checked)}
          className="mr-2"
        />

        {/* 分组名称 */}
        <label
          htmlFor={`group-${group.id}`}
          className="flex-1 cursor-pointer text-sm"
        >
          {group.name}
          <span className="text-xs text-gray-500 ml-1">({group.accountNum})</span>
        </label>
      </div>

      {/* 子节点 */}
      {hasChildren && isExpanded && (
        <div>
          {group.children.map(child => (
            <TreeNode
              key={child.id}
              group={child}
              selectedIds={selectedIds}
              expandedIds={expandedIds}
              onToggleExpand={onToggleExpand}
              onToggleSelect={onToggleSelect}
              searchTerm={searchTerm}
              level={level + 1}
            />
          ))}
        </div>
      )}
    </div>
  )
}

export const AssignAccountGroupDialog = forwardRef<AssignAccountGroupDialogRef, AssignAccountGroupDialogProps>(
  ({ onSuccess }, ref) => {
    const [open, setOpen] = useState(false)
    const [currentAccount, setCurrentAccount] = useState<Account | null>(null)
    const [selectedGroupIds, setSelectedGroupIds] = useState<number[]>([])
    const [searchTerm, setSearchTerm] = useState('')
    const [expandedIds, setExpandedIds] = useState<Set<number>>(new Set())

    // 获取分组列表
    const { data: groups = [] } = useQueryAccountGroup()

    // 展开/收起节点
    const handleToggleExpand = (id: number) => {
      const newExpanded = new Set(expandedIds)
      if (newExpanded.has(id)) {
        newExpanded.delete(id)
      } else {
        newExpanded.add(id)
      }
      setExpandedIds(newExpanded)
    }

    // 选择/取消选择节点
    const handleToggleSelect = (id: number, checked: boolean) => {
      if (checked) {
        setSelectedGroupIds(prev => [...prev, id])
      } else {
        setSelectedGroupIds(prev => prev.filter(groupId => groupId !== id))
      }
    }

    useImperativeHandle(ref, () => ({
      open: (account: Account) => {
        setCurrentAccount(account)
        setSelectedGroupIds([])
        setSearchTerm('')
        setExpandedIds(new Set())
        setOpen(true)
      }
    }), [])

    const handleClose = () => {
      setOpen(false)
      setCurrentAccount(null)
      setSelectedGroupIds([])
      setSearchTerm('')
      setExpandedIds(new Set())
    }

    // 获取所有分组ID（扁平化）
    const getAllGroupIds = (groupList: GroupItem[]): number[] => {
      const ids: number[] = []
      const traverse = (groups: GroupItem[]) => {
        groups.forEach(group => {
          ids.push(group.id)
          if (group.children && group.children.length > 0) {
            traverse(group.children)
          }
        })
      }
      traverse(groupList)
      return ids
    }

    // 分配账户到分组
    const assignMutation = useRequest(
      async () => {
        if (!currentAccount || selectedGroupIds.length === 0) {
          throw new Error('请选择至少一个分组')
        }

        // 为每个选中的分组分配账户
        const promises = selectedGroupIds.map(groupId =>
          MatrixModule.addAccountToGroup({
            groupId,
            accountIds: [currentAccount.id]
          })
        )

        await Promise.all(promises)
      },
      {
        actionName: '分配账户到分组',
        onSuccess: () => {
          handleClose()
          onSuccess?.()
          // 刷新相关查询
          queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.GROUP_ACCOUNT_LIST] })
        }
      }
    )

    const handleSubmit = () => {
      assignMutation.mutate()
    }

    const handleSelectAll = () => {
      const allGroupIds = getAllGroupIds(groups)
      setSelectedGroupIds(allGroupIds)
    }

    const handleUnselectAll = () => {
      setSelectedGroupIds([])
    }

    return (
      <Dialog open={open} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>账号分组</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {/* 当前账号信息 */}
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">当前账号:</span>
              <span className="text-sm font-medium text-red-500">
                {currentAccount?.nickname || currentAccount?.orgNickname || '未知账户'}
              </span>
            </div>

            {/* 搜索框 */}
            <Input
              placeholder="搜索分组"
              value={searchTerm}
              onChange={e => setSearchTerm(e.target.value)}
            />

            {/* 分组树型列表 */}
            <div className="max-h-[300px] overflow-y-auto border rounded-md p-3">
              {/* 全选/取消全选 */}
              <div className="flex items-center space-x-2 pb-2 border-b mb-2">
                <Checkbox
                  id="select-all"
                  checked={groups.length > 0 && getAllGroupIds(groups).every(id => selectedGroupIds.includes(id))}
                  onCheckedChange={checked => {
                    if (checked) {
                      handleSelectAll()
                    } else {
                      handleUnselectAll()
                    }
                  }}
                />
                <label htmlFor="select-all" className="text-sm font-medium">
                  全部
                </label>
              </div>

              {/* 树型分组选项 */}
              {groups.length === 0 ? (
                <div className="text-center text-sm text-muted-foreground py-4">
                  暂无分组数据
                </div>
              ) : (
                <div className="space-y-1">
                  {groups.map(group => (
                    <TreeNode
                      key={group.id}
                      group={group}
                      selectedIds={selectedGroupIds}
                      expandedIds={expandedIds}
                      onToggleExpand={handleToggleExpand}
                      onToggleSelect={handleToggleSelect}
                      searchTerm={searchTerm}
                      level={0}
                    />
                  ))}
                </div>
              )}
            </div>

            {/* 已选择数量 */}
            <div className="text-sm text-muted-foreground">
              已选择 {selectedGroupIds.length} 个分组
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={assignMutation.isPending}
            >
              取消
            </Button>
            <Button
              type="button"
              onClick={handleSubmit}
              disabled={assignMutation.isPending || selectedGroupIds.length === 0}
            >
              {assignMutation.isPending ? '分配中...' : '确定'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    )
  }
)

AssignAccountGroupDialog.displayName = 'AssignAccountGroupDialog'

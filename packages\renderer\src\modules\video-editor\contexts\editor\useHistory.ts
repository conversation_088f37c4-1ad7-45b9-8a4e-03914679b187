import { useCallback, useEffect, useMemo, useState } from 'react'
import { cloneDeep, isEqual } from 'lodash'
import { Track } from '@/modules/video-editor/types'

interface HistoryState {
  past: Track[][]
  present: Track[]
  future: Track[][]
}

export type HistoryHook = {
  undo(): void
  redo(): void
  canUndo: boolean
  canRedo: boolean
}

export function useHistory(
  tracks: Track[],
  setTracks: (tracks: Track[]) => void,
): HistoryHook {
  const [history, setHistory] = useState<HistoryState>({
    past: [],
    present: tracks,
    future: [],
  })

  useEffect(() => {
    setHistory(prev => {
      // Don't record history if this change was from undo/redo
      if (isEqual(prev.present, tracks)) return prev

      return {
        past: [...prev.past, prev.present],
        present: tracks,
        future: [],
      }
    })
  }, [tracks])

  const undo = useCallback(() => {
    setHistory(prev => {
      if (prev.past.length === 0) return prev

      const newPast = prev.past.slice(0, -1)
      const newPresent = prev.past[prev.past.length - 1]

      setTracks(cloneDeep(newPresent))

      return {
        past: newPast,
        present: newPresent,
        future: [prev.present, ...prev.future],
      }
    })
  }, [])

  const redo = useCallback(() => {
    setHistory(prev => {
      if (prev.future.length === 0) return prev

      const newFuture = prev.future.slice(1)
      const newPresent = prev.future[0]

      // Update tracks directly
      setTracks(newPresent)

      return {
        past: [...prev.past, prev.present],
        present: newPresent,
        future: newFuture,
      }
    })
  }, [])

  return {
    undo,
    redo,
    canUndo: useMemo(() => history.past.length > 0, [history]),
    canRedo: useMemo(() => history.future.length > 0, [history]),
  }
}

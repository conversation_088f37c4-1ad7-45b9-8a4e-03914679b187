import React, { useCallback, useState } from 'react'
import { genForm } from '@/libs/tools/form'
import { useFormContext } from 'react-hook-form'
import { z } from 'zod'
import { Input } from '@/components/ui/input'
import { useModal, useModalContext } from '@/libs/tools/modal'
import { <PERSON><PERSON><PERSON>ooter, ModalHeader } from '@/components/modal'
import { ResourceModule } from '@/libs/request/api/resource'
import { useQueryClient } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'

const CreateForm = genForm(
  z.object({
    title: z.string().min(1, '请输入脚本名称'),
  }),
  {
    fields: {
      title: {
        label: '脚本名称',
        render: ({ field }) => {
          const { setValue } = useFormContext()

          return (
            <div className="relative">
              <Input
                {...field}
                className="pr-16"
                onChange={e => setValue('title', e.currentTarget.value.slice(0, 20))}
              />
              <span className="absolute top-1/2 -translate-y-1/2 right-2 text-muted-foreground text-sm">
                {field.value.length}/20
              </span>
            </div>
          )
        },
      },
    },
  },
)

const UpdateForm = genForm(
  z.object({
    title: z.string().min(1, '请输入脚本名称'),
  }),
  {
    fields: {
      title: {
        label: '脚本名称',
        render: ({ field }) => {
          const { setValue } = useFormContext()

          return (
            <div className="relative">
              <Input
                {...field}
                className="pr-16"
                onChange={e => setValue('title', e.currentTarget.value.slice(0, 20))}
              />
              <span className="absolute top-1/2 -translate-y-1/2 right-2 text-muted-foreground text-sm">
                {field.value.length}/20
              </span>
            </div>
          )
        },
      },
    },
  },
)

const DuplicateForm = genForm(
  z.object({
    title: z.string().min(1, '请输入脚本名称'),
  }),
  {
    fields: {
      title: {
        label: '脚本名称',
        render: ({ field }) => {
          const { setValue } = useFormContext()

          return (
            <div className="relative">
              <Input
                {...field}
                className="pr-16"
                onChange={e => setValue('title', e.currentTarget.value.slice(0, 20))}
              />
              <span className="absolute top-1/2 -translate-y-1/2 right-2 text-muted-foreground text-sm">
                {field.value.length}/20
              </span>
            </div>
          )
        },
      },
    },
  },
)

function CreateModal({ projectId }: { projectId: number }) {
  const queryClient = useQueryClient()
  const { close } = useModalContext()
  const [pending, setPending] = useState(false)

  return (
    <>
      <ModalHeader title="创建混剪脚本" />
      <CreateForm
        defaultValues={{ title: '' }}
        onSubmit={async data => {
          setPending(true)
          try {
            await ResourceModule.script.create({
              projectId,
              title: data.title,
              content: '[]',
              fullContent: '[]',
            })
            await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.SCRIPT_LIST] })
            close()
          } finally {
            setPending(false)
          }
        }}
      >
        <ModalFooter pending={pending} />
      </CreateForm>
    </>
  )
}

function UpdateModal({ id, title }: { id: number; title: string }) {
  const queryClient = useQueryClient()
  const { close } = useModalContext()
  const [pending, setPending] = useState(false)

  return (
    <>
      <ModalHeader title="编辑混剪脚本" />
      <UpdateForm
        defaultValues={{ title }}
        onSubmit={async data => {
          setPending(true)
          try {
            await ResourceModule.script.rename({ id, title: data.title })
            await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.SCRIPT_LIST] })
            close()
          } finally {
            setPending(false)
          }
        }}
      >
        <ModalFooter pending={pending} />
      </UpdateForm>
    </>
  )
}

function DuplicateModal({
  id,
  title,
  projectId,
}: {
  id: number
  title: string
  projectId: number
}) {
  const queryClient = useQueryClient()
  const { close } = useModalContext()
  const [pending, setPending] = useState(false)

  return (
    <>
      <ModalHeader title="复制混剪脚本" />
      <DuplicateForm
        defaultValues={{ title: `${title} - 副本` }}
        onSubmit={async data => {
          setPending(true)
          try {
            await ResourceModule.script.duplicate({ id, title: data.title, projectId })
            await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.SCRIPT_LIST] })
            close()
          } finally {
            setPending(false)
          }
        }}
      >
        <ModalFooter pending={pending} />
      </DuplicateForm>
    </>
  )
}

export function useCreateScript() {
  const modal = useModal()
  return useCallback(
    (projectId: number) =>
      modal({
        content: <CreateModal projectId={projectId} />,
      }),
    [],
  )
}

export function useUpdateScript() {
  const modal = useModal()
  return useCallback(
    (id: number, title: string) =>
      modal({
        content: <UpdateModal id={id} title={title} />,
      }),
    [],
  )
}

export function useDuplicateScript() {
  const modal = useModal()
  return useCallback(
    (id: number, title: string, projectId: number) =>
      modal({
        content: <DuplicateModal id={id} title={title} projectId={projectId} />,
      }),
    [],
  )
}

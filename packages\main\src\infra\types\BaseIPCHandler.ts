import { ipcMain } from 'electron'
import IPCClients from '@app/shared/types/ipc-clients.js'
import { OnModuleInit } from '@nestjs/common'

/**
 * IPC处理器抽象基类
 * 提供统一的IPC注册和错误处理
 */
export abstract class BaseIPCHandler<
  IPCKey extends keyof IPCClients,
  T<PERSON>lient extends IPCClients[IPCKey] = IPCClients[IPCKey],
> implements OnModuleInit {

  /**
   * 平台前缀，用于隔离不同平台的IPC通道
   */
  protected abstract readonly platformPrefix: IPCKey

  /**
   * 构造完整的IPC通道名称
   * @param method 方法名
   * @returns 完整的IPC通道名称，如 'editor:getAccounts'
   */
  protected getChannelName<
    TMethod extends keyof TClient
  >(method: TMethod): string {
    return `${this.platformPrefix}:${method as string}`
  }

  /**
   * 注册IPC处理程序
   * @param method 方法名
   * @param handler 处理函数
   */
  protected registerHandler<
    TMethod extends keyof TClient,
    THandler = TClient[TMethod]
  >(
    method: TMeth<PERSON>,
    handler: <PERSON>Hand<PERSON>,
  ): void {
    const channel = this.getChannelName(method)

    ipcMain.handle(channel, async (_: any, data?: any) => {
      try {
        // @ts-ignore
        return Promise.resolve(handler(data))
      }
      catch (error: unknown) {
        console.error(`[${this.platformPrefix}] Error handling IPC ${method as string}:`, error)
        // 统一错误格式
        throw {
          code: error && typeof error === 'object' && 'code' in error ? error.code : 'UNKNOWN_ERROR',
          message: error && typeof error === 'object' && 'message' in error ? String(error.message) : '未知错误',
          details: error,
        }
      }
    })

    // console.log(`[${this.platformPrefix}] Registered IPC handler: ${channel}`)
  }

  /**
   * 注册所有IPC处理程序
   */
  abstract registerAll(): void

  onModuleInit() {
    this.registerAll()
  }
}

import React, { memo, useCallback } from 'react'

import { TextOverlay } from '@clipnest/remotion-shared/types'
import { ResourceType } from '@app/shared/types/resource-cache.types'

import { useInfiniteQueryFontStyleList } from '@/hooks/queries/useQueryTextStyle'

import { FontStyleResource } from '@/types/resources'
import { useFontManager } from '../../../hooks/useFontManager'
import { useResourceLoadingStore } from '../../../hooks/resource/useResourceLoadingStore'
import { cacheManager } from '@/libs/cache/cache-manager'

import InfiniteResourceList from '@/components/InfiniteResourceList'
import { EnhancedTextRenderer } from '@/modules/video-editor/components/overlays/text/enhanced-layer-text-renderer'
import { useResource } from '../../../hooks/resource/useResource'

/**
 * 构建文本叠加层的可重用工具函数
 * @param fontStyleResource 字体样式资源
 * @param content 字体样式内容
 * @param options 可选配置项
 */
const buildTextOverlay = (
  fontStyleResource: FontStyleResource.FontStyle,
  content: FontStyleResource.FontStyleContent,
  options: {
    isPreview?: boolean
    width?: number
    height?: number
    left?: number
    top?: number
    textContent?: string
    baseOverlay?: Partial<TextOverlay>
  } = {}
): TextOverlay => {
  const {
    isPreview = false,
    width = isPreview ? 80 : 300,
    height = isPreview ? 80 : 50,
    left = isPreview ? 0 : 100,
    top = isPreview ? 0 : 100,
    textContent = isPreview ? '花体' : '默认文字',
    baseOverlay = {}
  } = options

  // 基础样式默认值
  const baseStyles = {
    fontSize: isPreview ? 28 : 150,
    fontWeight: 'normal' as const,
    color: content.textColor || '#ffffff',
    fontFamily: content.fontName || 'Arial',
    fontStyle: content.italic ? 'italic' as const : 'normal' as const,
    underlineEnabled: Boolean(content.underline),
    textAlign: 'center' as const,
    backgroundColor: content.backgroundColor || 'transparent',
    zIndex: 20
  }

  // 轮廓（stroke）相关默认值
  const strokeStyles = {
    strokeEnabled: Boolean(content.borderWidth),
    strokeWidth: content.borderWidth,
    strokeColor: content.borderColor,
  }

  // 阴影（shadow）相关默认值
  const shadowStyles = {
    shadowEnabled: Boolean(content.shadowDistance),
    shadowDistance: content.shadowDistance,
    shadowAngle: content.shadowAngle,
    shadowBlur: content.shadowBlur,
    shadowColor: content.shadowColor,
    shadowOpacity: content.shadowColorAlpha,
  }

  // 起泡字相关
  const additionalStyles = {
    backgroundImage: undefined,
    bubbleTextRect: undefined,
  }

  return {
    ...baseOverlay,
    id: baseOverlay.id || fontStyleResource.id,
    type: baseOverlay.type || 'text' as any,
    src: fontStyleResource.content.fontPath,
    content: textContent,
    left: baseOverlay.left ?? left,
    top: baseOverlay.top ?? top,
    width: baseOverlay.width ?? width,
    height: baseOverlay.height ?? height,
    durationInFrames: baseOverlay.durationInFrames ?? 90,
    from: baseOverlay.from ?? 0,
    rotation: baseOverlay.rotation ?? 0,
    isDragging: baseOverlay.isDragging ?? false,
    styles: {
      ...baseOverlay.styles,
      ...baseStyles,
      ...strokeStyles,
      ...shadowStyles,
      ...additionalStyles,
    }
  } as TextOverlay
}

interface FontStyleSelectorProps {
  onFontStyleSelect: (fontStyleResource: FontStyleResource.FontStyle, previewOverlay: TextOverlay) => void
  baseOverlay?: Partial<TextOverlay>
  className?: string
}

const FontStyleSelector: React.FC<FontStyleSelectorProps> = ({
  onFontStyleSelect,
  baseOverlay,
  className = 'h-full'
}) => {
  const { downloadResourceToCache } = useResource()
  const { isFontStyleLoaded, loadFont } = useFontManager()
  const { isResourceLoading } = useResourceLoadingStore()

  const infiniteFontStyleQuery = useInfiniteQueryFontStyleList({
    pageNo: 1,
    pageSize: 50
  })

  // 预加载当前页面的所有字体
  React.useEffect(() => {
    const preloadVisibleFonts = async () => {
      if (infiniteFontStyleQuery.data?.pages) {
        const allItems = infiniteFontStyleQuery.data.pages.flatMap(page => page.list || [])

        // 限制预加载数量，防止无限加载
        const maxPreloadCount = 10
        let processedCount = 0

        // 过滤出需要加载的字体
        const fontsToLoad = allItems.filter(item => {
          const { content } = item
          const fontPath = content.fontPath

          if (!content.fontName || !fontPath) {
            return false
          }

          // 检查字体是否已经在 DOM 中加载
          const isFontLoaded = isFontStyleLoaded(fontPath)
          if (isFontLoaded) {
            return false
          }

          // 检查字体是否正在加载中
          const isLoading = isResourceLoading(fontPath, ResourceType.FONT)
          if (isLoading) {
            return false
          }

          // 检查字体是否已经下载到本地缓存
          const localPath = cacheManager.resource.getResourcePathSync(ResourceType.FONT, fontPath)
          if (localPath) {
            // 如果已经下载但未加载到 DOM，只需要加载到 DOM
            console.debug('[字体预加载] 字体已下载，仅需加载到 DOM:', content.fontName)
            return true
          }

          // 字体未下载，需要下载并加载
          return true
        }).slice(0, maxPreloadCount) // 限制预加载数量

        console.debug(`[字体预加载] 需要处理 ${fontsToLoad.length} 个字体 (最大 ${maxPreloadCount} 个)`)

        // 逐个处理字体，避免并发下载
        for (const item of fontsToLoad) {
          if (processedCount >= maxPreloadCount) {
            console.debug('[字体预加载] 已达到最大预加载数量，停止预加载')
            break
          }

          const { content } = item
          const fontPath = content.fontPath

          try {
            await loadFont(fontPath, content.fontName)
            console.debug('[字体预加载] 字体处理成功:', content.fontName)
            processedCount++
          } catch (error) {
            console.warn('[字体预加载] 字体处理失败:', content.fontName, error)
          }
        }
      }
    }

    preloadVisibleFonts()
  }, [infiniteFontStyleQuery.data, isFontStyleLoaded, loadFont, isResourceLoading])

  /**
   * 按需下载字体样式资源
   */
  const downloadFontStyleOnDemand = useCallback(async (fontStyleResource: FontStyleResource.FontStyle) => {
    try {
      const fontPath = fontStyleResource.content.fontPath

      // 检查字体是否已经下载到本地缓存
      const existingLocalPath = cacheManager.resource.getResourcePathSync(ResourceType.FONT, fontPath)
      if (existingLocalPath) {
        console.debug('[字体下载] 字体已存在于缓存，跳过下载:', fontStyleResource.content.fontName)
        return true
      }

      // 检查是否正在下载
      if (isResourceLoading(fontPath, ResourceType.FONT)) {
        console.debug('[字体下载] 字体正在下载中，跳过:', fontStyleResource.content.fontName)
        return false
      }

      console.debug('[字体下载] 开始下载字体:', fontStyleResource.content.fontName)

      // 下载字体到缓存
      const localPath = await downloadResourceToCache({
        url: fontPath,
        resourceType: ResourceType.FONT,
        version: '1.0'
      })

      if (localPath) {
        console.debug('[字体下载] 字体下载成功:', fontStyleResource.content.fontName)
        return true
      } else {
        console.warn('[字体下载] 字体下载失败:', fontPath)
        return false
      }
    } catch (error) {
      console.error('[字体下载] 字体下载异常:', error)
      return false
    }
  }, [downloadResourceToCache, isResourceLoading])

  const handleFontStyleSelect = useCallback(
    async (fontStyleResource: FontStyleResource.FontStyle) => {
      try {
        console.log('[花体字选择] 开始处理花体字选择:', fontStyleResource.content.fontName)

        // 按需下载/IPC获取本地花体字资源（仅在用户点击使用时）
        const downloadSuccess = await downloadFontStyleOnDemand(fontStyleResource)
        if (!downloadSuccess) {
          console.warn('[花体字选择] 花体字资源下载失败，但仍继续处理')
        }

        // 构建预览覆盖层，用于传递给回调函数
        const previewOverlay = buildTextOverlay(fontStyleResource, fontStyleResource.content, {
          isPreview: false,
          baseOverlay
        })

        onFontStyleSelect(fontStyleResource, previewOverlay)

        console.log('[花体字选择] 花体字选择处理成功')
      } catch (error) {
        console.error('[花体字选择] 处理花体字选择失败:', error)
      }
    },
    [downloadFontStyleOnDemand, onFontStyleSelect, baseOverlay]
  )

  const renderTextStyleItem = useCallback((fontStyleResource: FontStyleResource.FontStyle) => {
    const { content, title } = fontStyleResource
    const fontPath = content.fontPath

    const isFontLoaded = isFontStyleLoaded(fontPath)

    const previewOverlay = buildTextOverlay(fontStyleResource, content, {
      isPreview: true,
      baseOverlay
    })

    // 如果字体已加载，更新字体名称
    if (isFontLoaded && content.fontName) {
      previewOverlay.styles.fontFamily = `"${content.fontName}"`
    }

    const containerStyle: React.CSSProperties = {
      width: '100%',
      height: '100%',
    }

    return (
      <div
        key={fontStyleResource.id.toString()}
        onClick={() => handleFontStyleSelect(fontStyleResource)}
        className="group relative overflow-hidden border bg-gray-200 dark:bg-background rounded border-white/10 transition-all dark:hover:border-white/20 hover:border-blue-500/80 cursor-pointer aspect-square w-20"
      >
        <div className="h-full w-full flex items-center justify-center rounded">
          <div
            className="text-base transform-gpu transition-transform group-hover:scale-102 dark:text-white text-gray-900/90 size-full flex justify-center items-center"
          >
            <EnhancedTextRenderer
              overlay={previewOverlay}
              containerStyle={containerStyle}
              isPreview={true}
            />
          </div>
        </div>

        {/* Font Name Label */}
        <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white text-xs p-1 truncate">
          {title}
        </div>
      </div>
    )
  },
  [isFontStyleLoaded, handleFontStyleSelect, baseOverlay]
  )

  return (
    <div className={className}>
      <InfiniteResourceList
        queryResult={infiniteFontStyleQuery}
        renderItem={renderTextStyleItem}
        emptyText="没有找到花体字样式"
        loadingText="加载花体字样式中..."
        itemsContainerClassName="flex flex-wrap gap-3 p-2"
      />
    </div>
  )
}

export default memo(FontStyleSelector)

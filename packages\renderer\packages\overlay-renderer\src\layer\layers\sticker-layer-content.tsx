import React, { memo } from 'react'
import { StickerOverlay } from '@clipnest/remotion-shared/types'
import { useRenderContext } from '../../render.context'
import { useCurrentFrame } from 'remotion'
import { animationTemplates } from '@clipnest/remotion-shared/constants'

interface StickerLayerContentProps {
  overlay: StickerOverlay
  onUpdate?: (updates: Partial<StickerOverlay>) => void
}

// 加载中占位组件
const LoadingPlaceholder = memo(() => (
  <div
    style={{
      width: '100%',
      height: '100%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'rgba(0, 0, 0, 0.1)',
      borderRadius: '4px',
    }}
  >
    加载中...
  </div>
))
LoadingPlaceholder.displayName = 'LoadingPlaceholder'

// 贴纸未找到占位组件
const NotFoundPlaceholder = memo(() => (
  <div
    style={{
      width: '100%',
      height: '100%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundColor: 'rgba(255, 0, 0, 0.1)',
      borderRadius: '4px',
    }}
  >
    贴纸未找到
  </div>
))
NotFoundPlaceholder.displayName = 'NotFoundPlaceholder'

// 主组件
export const StickerLayerContent: React.FC<StickerLayerContentProps> = ({ overlay }) => {
  const { playerMetadata: { fps } } = useRenderContext()
  const frame = useCurrentFrame()
  const isExitPhase = frame >= overlay.durationInFrames - fps

  const enterAnimation
        = !isExitPhase && overlay.styles.animation?.enter
          ? animationTemplates[overlay.styles.animation.enter]?.enter(
            frame,
            overlay.durationInFrames,
          )
          : {}

  const exitAnimation
        = isExitPhase && overlay.styles.animation?.exit
          ? animationTemplates[overlay.styles.animation.exit]?.exit(
            frame,
            overlay.durationInFrames,
          )
          : {}

  const imageStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    objectPosition: overlay.styles.objectPosition,
    opacity: overlay.styles.opacity,
    transform: overlay.styles.transform || 'none',
    filter: overlay.styles.filter || 'none',
    borderRadius: overlay.styles.borderRadius || '0px',
    boxShadow: overlay.styles.boxShadow || 'none',
    border: overlay.styles.border || 'none',
    ...(isExitPhase ? exitAnimation : enterAnimation),
  }

  /**
       * Create a container style that includes padding and background color
       */
  const containerStyle: React.CSSProperties = {
    width: '100%',
    height: '100%',
    padding: overlay.styles.padding || '0px',
    backgroundColor: overlay.styles.paddingBackgroundColor || 'transparent',
    display: 'flex', // Use flexbox for centering
    alignItems: 'center',
    justifyContent: 'center',
  }

  return (
    <div
      style={{
        width: '100%',
        height: '100%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        borderRadius: '4px',
        opacity: overlay.styles?.opacity || 1,
        transform: `rotate(${overlay.rotation || 0}deg)`,
        ...containerStyle
      }}
    >
      <img
        src={overlay.localSrc || overlay.src}
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'contain',
          ...imageStyle,
        }}
        alt="贴纸"
      />
    </div>
  )
}

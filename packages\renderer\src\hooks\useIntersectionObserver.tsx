import React, { useRef, useEffect, useMemo } from 'react'

type UseIntersectionObserverOptions = {
  onIntersect: () => void
  deps?: React.DependencyList
  root?: Element | null
  rootMargin?: string
  threshold?: number
}

export function useIntersectionObserver({
  onIntersect,
  deps = [],
  root = null,
  rootMargin = '0px',
  threshold = 0,
}: UseIntersectionObserverOptions) {
  const ref = useRef<HTMLDivElement | null>(null)

  useEffect(() => {
    if (!ref.current) return
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          onIntersect()
        }
      },
      { root, rootMargin, threshold },
    )
    observer.observe(ref.current)
    return () => observer.disconnect()
  }, [ref.current, root, rootMargin, threshold, ...deps])

  const Component = useMemo(
    () =>
      function IntersectionTrigger() {
        return <div ref={ref} className="h-px opacity-0" aria-hidden />
      },
    [],
  )

  return Component
}

import localforage from 'localforage'

import { INDEXEDDB_DATABASE_NAME } from '@/constants/system'

export abstract class SubCacheManager {

  protected readonly store: LocalForage

  constructor(protected parent: ICacheManager, storeName?: string, description?: string) {
    parent.register(this)

    if (storeName) {
      this.store = localforage.createInstance({
        name: INDEXEDDB_DATABASE_NAME,
        storeName,
        description
      })
    }
  }

  init?(): void

  abstract cleanup(now: number, maxAge: number): void
}

export interface ICacheManager {
  register(subCacheManager: SubCacheManager): void
}

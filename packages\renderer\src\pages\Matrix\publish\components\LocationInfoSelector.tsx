import React from 'react'
import { <PERSON>, FieldErro<PERSON>, Controller } from 'react-hook-form'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { CHINA_PROVINCES } from '@/constants/system'

interface LocationInfoSectionProps {
  control: Control<any>
  errors?: FieldErrors
}

export const LocationInfoSection: React.FC<LocationInfoSectionProps> = ({
  control,
  errors
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>位置信息</CardTitle>
        <CardDescription>
          配置位置挂载相关信息
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center gap-6">
          <div className="space-y-2">
            <Label htmlFor="poiId" className="flex items-center gap-2">
              省份 <span className="text-red-500">*</span>
            </Label>
            <Controller
              name="poi.poiId"
              control={control}
              render={({ field }) => (
                <Select onValueChange={field.onChange} value={field.value}>
                  <SelectTrigger className={((errors?.poi as any) as any)?.poiId ? 'border-red-500' : ''}>
                    <SelectValue placeholder="请选择省份" />
                  </SelectTrigger>
                  <SelectContent>
                    {CHINA_PROVINCES.map(province => (
                      <SelectItem key={province.id} value={province.id}>
                        {province.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
            {(errors?.poi as any)?.poiId && (
              <p className="text-sm text-red-500">{(errors?.poi as any)?.poiId.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="poiName" className="flex items-center gap-2">
              详细地址 <span className="text-red-500">*</span>
            </Label>
            <Input
              id="poiName"
              placeholder="请输入详细地址"
              {...control.register('poi.poiName')}
              className={(errors?.poi as any)?.poiName ? 'border-red-500' : ''}
            />
            {(errors?.poi as any)?.poiName && (
              <p className="text-sm text-red-500">{(errors?.poi as any)?.poiName.message}</p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
import { cn } from '@/components/lib/utils'
import { Input } from '@/components/ui/input'
import React from 'react'
import { NavLink, useSearchParams } from 'react-router'
import { Search } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { useProjectCollaboratorModal } from './modal-collaborator'
import { useProjectsContext } from './context'
import { useQueryTeamMemberByID } from '@/hooks/queries/useQueryTeam'
import { TokenManager } from '@/libs/storage'

type Route = {
  path: string
  title: string
}

const paths: Route[] = [
  { path: 'creative', title: '我的创意' },
  { path: 'material', title: '我的素材' },
  { path: 'works', title: '我的作品' },
  { path: 'recycle-bin', title: '回收站' },
]

export function Header() {
  const openCollaboratorModal = useProjectCollaboratorModal()
  const [searchParams, setSearchParams] = useSearchParams()
  const { currentProject } = useProjectsContext()
  const { data: currentMember } = useQueryTeamMemberByID(TokenManager.getUserId())

  return (
    <div className="pt-2 flex px-4 justify-between items-center whitespace-nowrap">
      <div className="flex bg-background/50 p-1 gap-0 text-sm xl:text-base xl:gap-4 rounded-lg inset-shadow-muted-foreground">
        {paths.map(({ path, title }) => (
          <NavLink
            key={path}
            to={path}
            className={({ isActive }) => {
              return cn(
                'text-secondary-foreground px-1.5 xl:px-4 py-1 rounded-md',
                isActive && 'bg-linear-to-b from-muted-foreground/70 to-muted-foreground/40',
                // isActive && 'border-b-3 border-primary text-primary',
              )
            }}
          >
            {title}
          </NavLink>
        ))}
      </div>
      <div className="flex items-center gap-2">
        {currentMember?.roles?.some(role => ['owner', 'admin', 'manager'].includes(role.code)) && (
          <Button
            className="text-sm"
            variant="outline"
            disabled={!currentProject}
            onClick={() => openCollaboratorModal(currentProject!)}
          >
            添加协作者
          </Button>
        )}
        <div className="relative">
          <Input
            placeholder="请输入关键词搜索"
            value={searchParams.get('keyword') || ''}
            onChange={e => setSearchParams(prev => (prev.set('keyword', e.target.value), prev))}
            className="pr-10"
          />
          <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground" />
        </div>
      </div>
    </div>
  )
}

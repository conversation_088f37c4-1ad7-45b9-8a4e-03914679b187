import { RenderableOverlay } from '@clipnest/remotion-shared/types'

/**
 * 渲染器需要的编辑器上下文接口
 * 这是一个最小化的接口，只包含渲染器实际需要的功能
 */
export interface RendererEditorContext {
  /**
   * 获取宽高比尺寸
   */
  getAspectRatioDimensions(): { width: number; height: number }

  /**
   * 组合时长（帧数）
   */
  durationInFrames: number

  /**
   * 视频播放器配置
   */
  videoPlayer: {
    /**
     * 帧率
     */
    fps: number

    /**
     * 当前帧（可选，用于编辑模式）
     */
    currentFrame: number
  }

  /**
   * 当前选中的 Overlay（可选，仅编辑模式需要）
   */
  selectedOverlay: {
    id: number
  } | null

  /**
   * 设置选中的 Overlay（可选，仅编辑模式需要）
   */
  setSelectedOverlay: (overlay: any) => void

  /**
   * 更新 Overlay（可选，仅编辑模式需要）
   */
  updateOverlay: (id: number, updater: any) => void
}

/**
 * Props for the Main component
 */
export type MainProps = {
  /**
   * 需要渲染的 Overlay 数组
   */
  readonly overlays: RenderableOverlay[]

  /**
   * 播放器元数据. 用于内部 Layer 的渲染计算工作
   */
  playerMetadata?: {
    /** Duration in frames of the composition */
    readonly durationInFrames: number

    /** Frames per second of the composition */
    readonly fps: number

    /** Width of the composition */
    readonly width: number

    /** Height of the composition */
    readonly height: number
  }

  /** Base URL for media assets (optional) */
  readonly baseUrl?: string
}

import React from 'react'
import { TextOverlay } from '@clipnest/remotion-shared/types'
import { AnimationSettings } from '../../shared/animation-preview'
import { animationTemplates } from '@clipnest/remotion-shared/constants'
import { useOverlayEditing } from '../../../contexts'

export const TextSettingsPanel: React.FC = () => {
  const { localOverlay: textOverlay, requestUpdate: updateOverlay } = useOverlayEditing<TextOverlay>()

  const handleEnterAnimationSelect = (animationKey: string) => {
    updateOverlay({
      styles: {
        animation: {
          ...textOverlay.styles.animation,
          enter: animationKey === 'none' ? undefined : animationKey,
        },

      }
    }, true)
  }

  const handleExitAnimationSelect = (animationKey: string) => {
    updateOverlay({
      styles: {
        animation: {
          ...textOverlay.styles.animation,
          exit: animationKey === 'none' ? undefined : animationKey,
        },
      }
    }, true)
  }

  return (
    <AnimationSettings
      animations={animationTemplates}
      selectedEnterAnimation={textOverlay.styles.animation?.enter}
      selectedExitAnimation={textOverlay.styles.animation?.exit}
      onEnterAnimationSelect={handleEnterAnimationSelect}
      onExitAnimationSelect={handleExitAnimationSelect}
    />
  )
}

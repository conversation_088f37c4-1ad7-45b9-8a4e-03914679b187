import React from 'react'
import { <PERSON>, Controller } from 'react-hook-form'
import { CalendarIcon } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { Calendar } from '@/components/ui/calendar'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { format } from 'date-fns'
import { zhCN } from 'date-fns/locale'
import { PeriodType } from '@/types/matrix/douyin'

interface ScheduledPublishSectionProps {
  control: Control<any>
}

export const ScheduledPublishSection: React.FC<ScheduledPublishSectionProps> = ({
  control
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>定时发布配置</CardTitle>
        <CardDescription>
          配置定时发布的具体时间和间隔设置
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 发布时间 */}
        <div className="space-y-2">
          <Label className="flex items-center gap-2">
            <CalendarIcon className="w-4 h-4" />
            发布时间 <span className="text-red-500">*</span>
          </Label>
          <Controller
            name="timeSetting.publishTime"
            control={control}
            render={({ field }) => {
              const currentDate = field.value ? new Date(field.value) : new Date()
              const dateStr = format(currentDate, 'yyyy-MM-dd')
              const timeStr = format(currentDate, 'HH:mm')

              const handleDateChange = (date: Date | undefined) => {
                if (date) {
                  const existingTime = field.value ? new Date(field.value) : new Date()
                  const newDateTime = new Date(date)
                  newDateTime.setHours(existingTime.getHours())
                  newDateTime.setMinutes(existingTime.getMinutes())
                  newDateTime.setSeconds(0)
                  field.onChange(newDateTime.getTime())
                }
              }

              const handleTimeChange = (timeValue: string) => {
                const [hours, minutes] = timeValue.split(':').map(Number)
                const existingDate = field.value ? new Date(field.value) : new Date()
                const newDateTime = new Date(existingDate)
                newDateTime.setHours(hours)
                newDateTime.setMinutes(minutes)
                newDateTime.setSeconds(0)
                field.onChange(newDateTime.getTime())
              }

              return (
                <div className="flex gap-2">
                  {/* 日期选择器 */}
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-[180px] justify-start text-left font-normal"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {field.value ? dateStr : <span>选择日期</span>}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0">
                      <Calendar
                        mode="single"
                        selected={field.value ? new Date(field.value) : undefined}
                        onSelect={handleDateChange}
                        locale={zhCN}
                      />
                    </PopoverContent>
                  </Popover>

                  {/* 时间选择器 */}
                  <Input
                    type="time"
                    value={field.value ? timeStr : ''}
                    onChange={e => handleTimeChange(e.target.value)}
                    className="w-[120px]"
                  />
                </div>
              )
            }}
          />
        </div>

        {/* 间隔设置 */}
        <div className="space-y-4">
          <div className="flex items-center gap-4">
            <div className="flex gap-4 items-center">
              <Label htmlFor="period">间隔</Label>
              <div className="flex items-center gap-2">
                <Controller
                  name="timeSetting.period"
                  control={control}
                  render={({ field }) => (
                    <Select
                      value={field.value?.toString() || '0'}
                      onValueChange={value => field.onChange(Number(value))}
                    >
                      <SelectTrigger className="w-20">
                        <SelectValue placeholder="0" />
                      </SelectTrigger>
                      <SelectContent>
                        {[0, 5, 10, 15, 20].map(i => (
                          <SelectItem key={i} value={i.toString()}>
                            {i}分钟
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>
            </div>
          </div>

          {/* 间隔类型 */}
          <div className="space-y-3">
            <Controller
              name="timeSetting.periodType"
              control={control}
              render={({ field }) => (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="single-account"
                      checked={field.value?.includes(PeriodType.SINGLE_ACCOUNT) || false}
                      onCheckedChange={checked => {
                        const currentValue = field.value || []
                        if (checked) {
                          field.onChange([...currentValue.filter(v => v !== PeriodType.SINGLE_ACCOUNT), PeriodType.SINGLE_ACCOUNT])
                        } else {
                          field.onChange(currentValue.filter(v => v !== PeriodType.SINGLE_ACCOUNT))
                        }
                      }}
                    />
                    <Label htmlFor="single-account" className="text-sm font-normal cursor-pointer">
                      单账号发布视频的间隔
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="multiple-accounts"
                      checked={field.value?.includes(PeriodType.MULTIPLE_ACCOUNTS) || false}
                      onCheckedChange={checked => {
                        const currentValue = field.value || []
                        if (checked) {
                          field.onChange([...currentValue.filter(v => v !== PeriodType.MULTIPLE_ACCOUNTS), PeriodType.MULTIPLE_ACCOUNTS])
                        } else {
                          field.onChange(currentValue.filter(v => v !== PeriodType.MULTIPLE_ACCOUNTS))
                        }
                      }}
                    />
                    <Label htmlFor="multiple-accounts" className="text-sm font-normal cursor-pointer">
                      多账号发布视频的间隔
                    </Label>
                  </div>
                </div>
              )}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

import React, { useState, useEffect } from 'react'
import { useParams } from 'react-router'
import { Loader2, RefreshCw } from 'lucide-react'
import { useQueryMediaList } from '@/hooks/queries/useQueryMaterial'
import { ResourceModule } from '@/libs/request/api/resource'
import { useQueryClient } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { MaterialResource, ResourceSource } from '@/types/resources'
import TreeList, { TreeNode, isValidFolderId } from '@/components/TreeList'
import { Button } from '@/components/ui/button'
import { useDeleteModal } from '@/components/modal/delete'
import MoveDialog from './components/MoveDialog'
import MediaTypeSelector from './components/MediaTypeSelector'
import MaterialFilterBar from './components/MaterialFilterBar'
import MediaItem from './components/MediaItem'
import Breadcrumbs from '@/components/Breadcrumbs'
import UploaderCard from './components/MaterialUpload'
import { useItemActions } from '@/hooks/useItemActions'
import { useSelection } from '@/hooks/useSelection'
import { useFolderData } from '@/hooks/useFolderData'
import { useFolderActions } from '@/hooks/useFolderActions'
import { useMediaActions } from '@/hooks/useMediaActions'
import { usePending } from '@/hooks/usePending'

const Material: React.FC = () => {
  const params = useParams()
  const queryClient = useQueryClient()
  const [activeTab, setActiveTab] = useState(0)
  const [orientation, setOrientation] = useState(MaterialResource.MediaStyle.HORIZONTAL)
  const [moveDialogOpen, setMoveDialogOpen] = useState(false)
  const [moveId, setMoveId] = useState('')
  const [moveType, setMoveType] = useState(ResourceSource.FOLDER)

  const { createItem, renameItem, deleteItem } = useItemActions()
  const folderActions = useFolderActions(createItem, renameItem, deleteItem, setMoveType, setMoveId, setMoveDialogOpen)
  const mediaActions = useMediaActions(renameItem, deleteItem, setMoveType, setMoveId, setMoveDialogOpen)
  const deleteModal = useDeleteModal()

  const [draggingItem, setDraggingItem] = useState<{
    id: string
    type: ResourceSource // MEDIA or FOLDER
  } | null>(null)

  const [filters, setFilters] = useState<MaterialResource.MaterialMediaParams>({
    projectId: Number(params.projectId),
    folderUuid: '',
    sortField: MaterialResource.SortField.UPLOAD_TIME,
    sortOrder: MaterialResource.SortOrder.ASC,
    createAtRange: [],
    durationRange: [],
    useCountRange: undefined, // 合成次数
    quoteCountRange: undefined, // 引用次数
    keyword: undefined,
    resType: undefined,
  })

  // 获取目录和素材数据
  const {
    treeData,
    isSuccess: isTreeSuccess,
    currentFolderId,
    setCurrentFolderId,
    folderPath,
    childFolders,
  } = useFolderData(Number(params.projectId))
  const { data: mediaList, isLoading } = useQueryMediaList(filters, isTreeSuccess && currentFolderId !== '')

  // 刷新列表数据
  const onRefresh = async () => {
    await Promise.all([
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATERIAL_MEDIA_LIST] }),
      queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATERIAL_DIRECTORY_LIST] }),
    ])
  }

  // 文件夹数据结构改成文件数据结构
  const folderAsMediaItems: MaterialResource.Media[] = childFolders.map(folder => ({
    fileId: folder.id,
    fileName: folder.label,
    folderUuid: folder.raw.parentId,
    childrenFolder: folder.children.length || 0,
    mediaNum: folder.raw.imageCount + folder.raw.videoCount,
    resType: 0, // 代表文件夹
    createTime: folder.raw.createdAt || new Date().toISOString(),
  }))

  //全选 选中
  const {
    selectedMediaItems,
    selectedFolderItems,
    setSelectedMediaItems,
    setSelectedFolderItems,
    toggleSelect,
    toggleSelectAll,
    allSelected,
    selectedCount,
    mediaCount,
  } = useSelection({
    mediaList,
    folderAsMediaItems,
    getMediaId: media => media.fileId,
    getFolderId: folder => folder.fileId,
  })

  useEffect(() => {
    setFilters(prevFilters => ({
      ...prevFilters,
      folderUuid: currentFolderId,
    }))
  }, [currentFolderId])

  useEffect(() => {
    if (!isTreeSuccess || !treeData || treeData.length === 0) return

    const firstFolderId = treeData[0].id

    // 当前无选中目录 或 选中的目录在 treeData 中已不存在
    if (!currentFolderId || !isValidFolderId(treeData, currentFolderId)) {
      setCurrentFolderId(firstFolderId)
      handleFolderClick(firstFolderId)
    }
  }, [isTreeSuccess, treeData, currentFolderId])

  const handleMove = async (targetFolderId: string) => {
    // 移动媒体文件
    if (selectedMediaItems.size > 0) {
      await ResourceModule.media.move({
        fileIds: Array.from(selectedMediaItems),
        folderUuid: targetFolderId,
      })
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATERIAL_MEDIA_LIST] })
    }

    // 移动文件夹
    if (selectedFolderItems.size > 0) {
      await ResourceModule.directory.move({
        folderIds: Array.from(selectedFolderItems),
        parentId: targetFolderId,
      })
      await queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATERIAL_DIRECTORY_LIST] })
    }

    setSelectedMediaItems(new Set())
    setSelectedFolderItems(new Set())
  }

  const handleMoveConfirm = async (selectedNode: TreeNode) => {
    if (moveType === ResourceSource.MULTI_SELECT) {
      handleMove(selectedNode.id)
    } else {
      await onRefresh()
    }
  }

  //放入回收站/彻底删除
  const handleBatchDelete = async (isPermanent: boolean) => {
    if (selectedMediaItems.size > 0) {
      await ResourceModule.media[isPermanent ? 'delete' : 'recycle']({
        fileIds: [...selectedMediaItems],
      })
    }
    if (selectedFolderItems.size > 0) {
      await ResourceModule.directory[isPermanent ? 'delete' : 'recycle']({
        folderIds: [...selectedFolderItems],
      })
    }
    await onRefresh()
    setSelectedMediaItems(new Set())
    setSelectedFolderItems(new Set())
  }

  const handleFolderClick = (folderId: string) => {
    setCurrentFolderId(folderId)
    queryClient.invalidateQueries({ queryKey: [QUERY_KEYS.MATERIAL_MEDIA_LIST] })
  }

  // 刷新选中状态
  useEffect(() => {
    setSelectedMediaItems(new Set())
    setSelectedFolderItems(new Set())
  }, [currentFolderId])

  return (
    <div className="p-4 flex flex-col flex-1 h-full w-full overflow-auto">
      <MediaTypeSelector
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        setFilters={setFilters}
        onCreateFolder={() =>
          createItem(ResourceSource.FOLDER, currentFolderId, {
            label: '文件夹名称',
            headerTitle: '文件夹',
          })} 
      />
      <MaterialFilterBar
        filters={filters}
        setFilters={setFilters}
        orientation={orientation}
        setOrientation={setOrientation}
      />
      <div className="border-t mt-2 mb-3" />
      <div className="flex flex-1 overflow-auto">
        <TreeList
          data={treeData}
          defaultExpandAll={true}
          className="w-64 flex-shrink-0 overflow-auto border-r"
          actions={folderActions}
          showEllipsis={true}
          selectedId={currentFolderId}
          onSelect={node => {
            handleFolderClick(node.id)
          }}
        />
        <div className="flex-1 overflow-auto">
          <div className="flex items-center justify-between text-sm text-gray-600 pl-4">
            {/* 面包屑导航 */}
            <Breadcrumbs folderPath={folderPath} currentFolderId={currentFolderId} onFolderClick={handleFolderClick} />
            <div className="flex justify-end items-center space-x-4">
              {selectedCount !== 0 && (
                <div>
                  <Button
                    variant="default"
                    size="sm"
                    className="bg-primary-highlight1 hover:bg-blue-400 text-white"
                    onClick={() => {
                      setMoveType(ResourceSource.MULTI_SELECT)
                      setMoveDialogOpen(true)
                    }}
                  >
                    移动到
                  </Button>
                  <Button
                    variant="default"
                    size="sm"
                    className="bg-primary-highlight1 hover:bg-blue-400 text-white ml-2"
                    onClick={() => {
                      deleteModal({
                        kind: '',
                        name: '所选文件',
                        danger: true,
                        buttons: ({ close }) => {
                          const { pending, withPending } = usePending()

                          return (
                            <>
                              <Button
                                variant="default"
                                className="min-w-[80px] h-8 ml-2 bg-white/5 text-white border hover:bg-primary-highlight1"
                                onClick={withPending(async () => {
                                  await handleBatchDelete(false)
                                  close()
                                })}
                              >
                                {pending ? <Loader2 className="animate-spin size-4" /> : '放入回收站'}
                              </Button>
                              <Button
                                variant="destructive"
                                className="min-w-[80px] h-8 ml-2 bg-destructive text-white border hover:bg-destructive/90"
                                onClick={withPending(async () => {
                                  await handleBatchDelete(true)
                                  close()
                                })}
                              >
                                {pending ? <Loader2 className="animate-spin size-4" /> : '彻底删除'}
                              </Button>
                            </>
                          )
                        },
                      })
                    }}
                  >
                    删除
                  </Button>
                </div>
              )}

              <span>素材总数：{mediaCount}</span>
              <label className="flex items-center cursor-pointer">
                <input
                  type="checkbox"
                  checked={allSelected}
                  onChange={toggleSelectAll}
                  className="mr-1 accent-primary-highlight1"
                />
                全选
              </label>
              <span> | </span>
              <span>已选 {selectedCount}</span>
              <div className="flex items-center">
                <RefreshCw className="inline-block w-4 h-4 mr-1 text-primary-highlight1" />
                <button className="hover:underline" onClick={onRefresh}>
                  刷新
                </button>
              </div>
            </div>
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center h-full">加载中...</div>
          ) : (
            <div className="flex flex-wrap gap-4 p-4 overflow-auto">
              <UploaderCard currentFolderId={currentFolderId} orientation={orientation} />
              {folderAsMediaItems.map(folder => (
                <MediaItem
                  key={folder.fileId}
                  data-type="folder"
                  orientation={orientation}
                  media={folder}
                  isSelected={selectedFolderItems.has(folder.fileId)}
                  isFolder={true}
                  actions={folderActions}
                  onToggleSelect={fileId => toggleSelect(fileId, true)}
                  onFolderClick={() => handleFolderClick(folder.fileId)}
                  draggable
                  onDragStart={() => setDraggingItem({ id: folder.fileId, type: ResourceSource.FOLDER })}
                  onDragEnd={() => setDraggingItem(null)}
                  onDragOver={e => e.preventDefault()} // 允许放置
                  onDrop={async e => {
                    e.preventDefault()
                    if (!draggingItem || draggingItem.id === folder.fileId) return

                    if (draggingItem.type === ResourceSource.MEDIA) {
                      // 移动文件到文件夹
                      await ResourceModule.media.move({
                        fileIds: [draggingItem.id],
                        folderUuid: folder.fileId,
                      })
                    } else if (draggingItem.type === ResourceSource.FOLDER) {
                      // 移动文件夹到文件夹
                      await ResourceModule.directory.move({
                        folderIds: [draggingItem.id],
                        parentId: folder.fileId,
                      })
                    }
                    await onRefresh()
                  }}
                />
              ))}

              {mediaList?.pages.map(page =>
                page.list.map(media => (
                  <MediaItem
                    key={media.fileId}
                    data-type="media"
                    orientation={orientation}
                    media={media}
                    isSelected={selectedMediaItems.has(media.fileId)}
                    isFolder={false}
                    actions={mediaActions}
                    onToggleSelect={fileId => toggleSelect(fileId, false)}
                    draggable
                    onDragStart={() => setDraggingItem({ id: media.fileId, type: ResourceSource.MEDIA })}
                    onDragEnd={() => setDraggingItem(null)}
                  />
                )),
              )}
            </div>
          )}
        </div>
      </div>
      <MoveDialog
        open={moveDialogOpen}
        moveId={moveId}
        moveType={moveType}
        onOpenChange={setMoveDialogOpen}
        onConfirm={handleMoveConfirm}
      />
    </div>
  )
}

export default Material

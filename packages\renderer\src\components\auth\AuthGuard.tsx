import React, { useEffect } from 'react'
import { Navigate, useLocation } from 'react-router-dom'
import { TokenManager } from '@/libs/storage'
import { PageLoading } from '@/components/LoadingIndicator'

interface AuthGuardProps {
  children: React.ReactNode
  /**
   * 是否需要认证，默认为true
   */
  requireAuth?: boolean
  /**
   * 未认证时的重定向路径，默认为'/login'
   */
  redirectTo?: string
  /**
   * 已认证但访问登录页时的重定向路径，默认为'/home/<USER>'
   */
  authenticatedRedirectTo?: string
}

/**
 * 认证路由守卫组件
 *
 * @example
 * // 需要认证的路由
 * <AuthGuard>
 *   <Dashboard />
 * </AuthGuard>
 *
 * @example
 * // 不需要认证的路由（如登录页）
 * <AuthGuard requireAuth={false}>
 *   <LoginPage />
 * </AuthGuard>
 */
export const AuthGuard: React.FC<AuthGuardProps> = ({
  children,
  requireAuth = true,
  redirectTo = '/login',
  authenticatedRedirectTo = '/home/<USER>'
}) => {
  const location = useLocation()
  const isLoggedIn = TokenManager.isLoggedIn()

  // 如果需要认证但用户未登录，重定向到登录页
  if (requireAuth && !isLoggedIn) {
    return <Navigate to={redirectTo} state={{ from: location }} replace />
  }

  // 如果不需要认证（如登录页）但用户已登录，重定向到首页
  if (!requireAuth && isLoggedIn) {
    return <Navigate to={authenticatedRedirectTo} replace />
  }

  return <>{children}</>
}

/**
 * 需要认证的路由守卫
 */
export const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <AuthGuard requireAuth={true}>
      {children}
    </AuthGuard>
  )
}

/**
 * 公开路由守卫（如登录页，已登录用户不应访问）
 */
export const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <AuthGuard requireAuth={false}>
      {children}
    </AuthGuard>
  )
}

/**
 * 检查认证状态的Hook
 */
export const useAuthStatus = () => {
  const [isLoggedIn, setIsLoggedIn] = React.useState(TokenManager.isLoggedIn())
  const [isLoading, setIsLoading] = React.useState(true)

  useEffect(() => {
    // 检查认证状态
    const checkAuthStatus = () => {
      const loggedIn = TokenManager.isLoggedIn()
      setIsLoggedIn(loggedIn)
      setIsLoading(false)
    }

    checkAuthStatus()

    // 监听storage变化
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'access_token' || e.key === 'token_expires_time') {
        checkAuthStatus()
      }
    }

    window.addEventListener('storage', handleStorageChange)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [])

  return { isLoggedIn, isLoading }
}

/**
 * 带加载状态的认证守卫
 */
export const AuthGuardWithLoading: React.FC<AuthGuardProps> = props => {
  const { isLoading } = useAuthStatus()

  if (isLoading) {
    return <PageLoading />
  }

  return <AuthGuard {...props} />
}

export default AuthGuard

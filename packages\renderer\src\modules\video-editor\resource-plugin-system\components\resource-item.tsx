import React, { ReactNode, useCallback } from 'react'
import { ResourceType } from '@app/shared/types/resource-cache.types'
import { ResourceCacheIndicator } from '@/modules/video-editor/resource-plugin-system/components/resource-cache-indicator'
import { ResourceCollectionIndicator } from '@/modules/video-editor/components/resource-collection-indicator'
import { InteractInfo } from '@/types/resources'
import { cn } from '@/components/lib/utils'
import { useResourceCacheStatus } from '../../hooks/resource/useResourceCacheStatus'

export interface ResourceItemProps {
  /**
   * 资源ID
   */
  id: string | number
  /**
   * 资源标题
   */
  title: string
  /**
   * 是否显示标题
   */
  showTitle?: boolean

  /**
   * 资源描述或副标题
   */
  description?: string | number
  /**
   * 资源缩略图URL
   */
  thumbnailUrl?: string
  /**
   * 默认图标 (Lucide图标)
   */
  icon?: ReactNode
  /**
   * 是否正在加载
   */
  isLoading?: boolean
  /**
   * 点击添加按钮的回调
   */
  onAdd?: () => void
  /**
   * 点击整个资源项的回调
   */
  onClick?: () => void

  /**
   * 自定义内容
   */
  children?: ReactNode
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 资源类型，用于检查本地缓存
   */
  resourceType?: ResourceType
  /**
   * 资源URL，用于检查本地缓存
   */
  resourceUrl?: string
  /**
   * 收藏状态变更回调
   */
  onCollectionChange?: (collected: boolean) => void
  /**
   * 是否显示收藏按钮
   */
  showCollectionButton?: boolean
  /**
   * 交互信息，包含收藏状态
   */
  interactInfo?: InteractInfo
  /**
   * 资源时长（毫秒）
   */
  durationMsec?: number
  version?: string
}

/**
 * 通用资源项组件
 * 用于展示资源列表中的单个资源项
 */
export function ResourceItem({
  id,
  title,
  thumbnailUrl,
  icon,
  onAdd,
  onClick,
  children,
  className = '',
  resourceType,
  resourceUrl,
  description,
  onCollectionChange,
  showCollectionButton = true,
  interactInfo,
  durationMsec,
  showTitle = false,
}: ResourceItemProps) {
  const { isCached, isChecking } = useResourceCacheStatus(resourceType, resourceUrl)

  const isCollected = interactInfo?.collected || false

  const cacheIndicator = resourceType && resourceUrl && (

    <ResourceCacheIndicator
      resourceType={resourceType}
      resourceUrl={resourceUrl}
      size={12}
      onDownloadAndAddToTimeline={onAdd}
      isCached={isCached}
      isChecking={isChecking}
    />
  )

  const collectionIndicator = resourceType && id && showCollectionButton && (
    <div className={cn(
      'absolute right-0 top-0 transition-opacity duration-200',
      isCollected ? 'opacity-100' : 'opacity-0 group-hover:opacity-100'
    )}
    >
      <ResourceCollectionIndicator
        resourceType={resourceType}
        resourceId={id}
        isCollected={isCollected}
        size={12}
        onCollectionChange={onCollectionChange}
      />
    </div>
  )

  // 处理拖拽开始事件
  const handleDragStart = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    if (!resourceType || !resourceUrl ) return

    // 设置拖拽数据
    const dragData = {
      resourceType,
      resourceUrl,
      id,
      title,
      durationMsec: durationMsec || 10000,
    }

    e.dataTransfer.setData('application/json', JSON.stringify(dragData))

    // 设置拖拽效果
    e.dataTransfer.effectAllowed = 'copy'

    // 设置拖拽图像（可选）
    if (thumbnailUrl) {
      const img = new Image()
      img.src = thumbnailUrl
      e.dataTransfer.setDragImage(img, 25, 25)
    }
  }, [resourceType, resourceUrl, id, title, durationMsec, thumbnailUrl])

  return (
    <div className="relative">
      <div
        className={`aspect-square ${className}`}
        onClick={onClick}
        draggable={!!resourceType && !!resourceUrl }
        onDragStart={handleDragStart}
      >
        <div
          className={cn(
            `group relative w-full h-full
            bg-muted/30
            rounded dark:bg-gray-800/40
            border border-gray-800/10 dark:border-gray-700/10
            hover:border-blue-500/20 dark:hover:border-blue-500/20
            hover:bg-blue-500/5 dark:hover:bg-blue-500/5
            transition-all overflow-hidden`,
            // 移除 isCached 状态，让 ResourceCacheIndicator 内部管理
            resourceType && resourceUrl ? 'cursor-grab active:cursor-grabbing' : ''
          )}
        >
          {
            description && (
              <div className="text-[10px] border border-gray-500 bg-gray-500/20 rounded px-1 h-4 flex items-center justify-center text-gray-500 absolute bottom-1 left-1">
                {description}
              </div>
            )
          }
          {thumbnailUrl ? (
            <div className="absolute inset-0 flex items-center justify-center">
              <img
                src={thumbnailUrl}
                alt={title}
                className="max-w-full max-h-full object-contain"
                loading="lazy"
              />
            </div>
          ) : icon && (
            <div className="absolute inset-0 flex items-center justify-center text-gray-400">
              {icon}
            </div>
          )}
          { (resourceType && resourceUrl ? cacheIndicator : null)}
          {collectionIndicator}
        </div>
      </div>
      {children}
      {
        showTitle && title && (
          <div className="text-sm text-gray-200 ">
            {title}
          </div>
        )
      }
    </div>
  )
}

export default ResourceItem

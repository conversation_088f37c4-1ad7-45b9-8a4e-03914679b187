import React from 'react'

import { <PERSON>gn<PERSON><PERSON><PERSON>, Mi<PERSON>, PaintBucket } from 'lucide-react'

import { CaptionStylePanel } from './caption-style-panel'
import { CaptionTimeline } from './caption-timeline'
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useEditorContext } from '@/modules/video-editor/contexts'
import { FPS } from '@/modules/video-editor/constants'

export const CaptionSetting: React.FC = () => {
  const { videoPlayer: { currentFrame } } = useEditorContext()
  const currentMs = (currentFrame / FPS) * 1000

  return (
    <Tabs defaultValue="captions" className="w-full">
      {/* Tab Navigation */}
      <TabsList className="w-full grid grid-cols-3 bg-gray-100/50 dark:bg-gray-800/50 backdrop-blur-sm rounded-sm border border-gray-200 dark:border-gray-700 gap-1">
        {/* Captions Tab */}
        <TabsTrigger
          value="captions"
          className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-gray-900 dark:data-[state=active]:text-white
            rounded-sm transition-all duration-200 text-gray-600 dark:text-zinc-400 hover:text-gray-900 dark:hover:text-zinc-200 hover:bg-gray-200/50 dark:hover:bg-gray-700/50"
        >
          <span className="flex items-center gap-2 text-xs">
            <AlignLeft className="w-3 h-3" />
            Captions
          </span>
        </TabsTrigger>

        {/* Display Tab */}
        <TabsTrigger
          value="display"
          className="data-[state=active]:bg-blue-500/20 data-[state=active]:text-gray-900 dark:data-[state=active]:text-white
            rounded-sm transition-all duration-200 text-gray-600 dark:text-zinc-400 hover:text-gray-900 dark:hover:text-zinc-200 hover:bg-gray-200/50 dark:hover:bg-gray-700/50"
        >
          <span className="flex items-center gap-2 text-xs">
            <PaintBucket className="w-3 h-3" />
            Style
          </span>
        </TabsTrigger>

        {/* Voice Tab (Coming Soon) */}
        <TabsTrigger
          value="voice"
          disabled
          className="cursor-not-allowed opacity-50 rounded-sm transition-all duration-200 text-gray-600 dark:text-zinc-400"
        >
          <span className="flex items-center gap-2 text-xs">
            <Mic className="w-3 h-3" />
            Voice
            <span className="text-[9px] ml-2 text-amber-700 dark:text-amber-400 font-medium bg-amber-100/50 dark:bg-yellow-800/50 rounded-sm px-1 py-0.5">
              SOON
            </span>
          </span>
        </TabsTrigger>
      </TabsList>

      {/* Tab Content */}
      <TabsContent
        value="display"
        className="space-y-4 mt-4 focus-visible:outline-none"
      >
        <CaptionStylePanel />
      </TabsContent>

      <TabsContent
        value="captions"
        className="space-y-4 mt-4 focus-visible:outline-none"
      >
        <CaptionTimeline currentMs={currentMs} />
      </TabsContent>
    </Tabs>
  )
}

import React from 'react'
import { Composition, registerRoot } from 'remotion'
import { Renderer } from './renderer'

const DURATION_IN_FRAMES = 0
const FPS = 30
const COMP_NAME = import.meta.env.VITE_RENDERER_VERSION

const RemotionRoot: React.FC = () => {
  const defaultMyCompProps: any = {
    overlays: [],
    durationInFrames: DURATION_IN_FRAMES,
    fps: FPS,
    width: 1920,
    height: 1920,
    src: '',
    setSelectedOverlayId: () => {
    },
    selectedOverlayId: null,
    changeOverlay: () => {
    },
  }

  return (
    <>
      <Composition
        id={COMP_NAME}
        component={Renderer}
        durationInFrames={DURATION_IN_FRAMES}
        fps={FPS}
        width={1920}
        height={1920}
        calculateMetadata={async ({ props }) => {
          const { durationInFrames, width, height } = props.playerMetadata!
          return {
            durationInFrames,
            width,
            height,
          }
        }}
        defaultProps={defaultMyCompProps}
      />
    </>
  )
}

registerRoot(RemotionRoot)

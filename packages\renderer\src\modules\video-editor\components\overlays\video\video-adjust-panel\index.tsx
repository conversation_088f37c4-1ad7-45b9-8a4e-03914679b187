import React from 'react'
import { SettingsTabs, TabItem } from '../../../shared/settings-tabs'
import { AdjustSetting } from './adjust-setting'
import { HSLSetting } from './hsl-setting'

export const VideoAdjustPanel: React.FC = () => {
  const tabs: TabItem[] = [
    {
      value: 'basic',
      label: '基础',
      content: (
        <AdjustSetting />
      )
    },
    {
      value: 'hsl',
      label: 'HSL',
      content: (
        <HSLSetting />
      )
    }
  ]

  return (
    <div className="space-y-2 flex flex-1 overflow-hidden h-full">
      <SettingsTabs tabs={tabs} defaultTab="basic" />
    </div>
  )
}

import { ipcRequestStateSync } from '@/libs/request/session/ipc-state-sync'
import { AuthState } from '@app/shared/types/auth-request.types'

// 存储键名常量
export const STORAGE_KEYS = {
  // 认证相关
  ACCESS_TOKEN: 'access_token',
  REFRESH_TOKEN: 'refresh_token',
  USER_ID: 'user_id',
  TOKEN_EXPIRES_TIME: 'token_expires_time',
  
  // 用户设置
  THEME: 'theme',

  // 租户设置
  TENANT_ID: 'tenant_id',
  
  // 其他
  USER_PREFERENCES: 'user_preferences',
} as const

// 存储值类型定义
export interface StorageValues {
  [STORAGE_KEYS.ACCESS_TOKEN]: string
  [STORAGE_KEYS.REFRESH_TOKEN]: string
  [STORAGE_KEYS.USER_ID]: number
  [STORAGE_KEYS.TOKEN_EXPIRES_TIME]: number
  [STORAGE_KEYS.THEME]: 'light' | 'dark'
  [STORAGE_KEYS.USER_PREFERENCES]: Record<string, any>
  [STORAGE_KEYS.TENANT_ID]: number,
}

/**
 * 检查localStorage是否可用
 */
export const isLocalStorageAvailable = (): boolean => {
  try {
    if (typeof window === 'undefined' || !window.localStorage) {
      return false
    }
    
    const testKey = '__test_storage__'
    window.localStorage.setItem(testKey, 'test')
    window.localStorage.removeItem(testKey)
    return true
  } catch (error) {
    console.error('localStorage不可用:', error)
    return false
  }
}

/**
 * 本地存储管理类
 */
class StorageManager {

  private readonly isAvailable: boolean

  constructor() {
    this.isAvailable = isLocalStorageAvailable()
  }

  /**
   * 设置存储值
   */
  setItem<K extends keyof StorageValues>(key: K, value: StorageValues[K]): void {
    if (!this.isAvailable) {
      console.warn('localStorage不可用，无法存储数据')
      return
    }

    try {
      const serializedValue = JSON.stringify(value)
      localStorage.setItem(key, serializedValue)
    } catch (error) {
      console.error(`存储数据失败 [${key}]:`, error)
    }
  }

  /**
   * 获取存储值
   */
  getItem<K extends keyof StorageValues>(key: K): StorageValues[K] | null {
    if (!this.isAvailable) {
      return null
    }

    try {
      const item = localStorage.getItem(key)
      if (item === null) {
        return null
      }
      return JSON.parse(item) as StorageValues[K]
    } catch (error) {
      console.error(`获取数据失败 [${key}]:`, error)
      return null
    }
  }

  /**
   * 移除存储值
   */
  removeItem<K extends keyof StorageValues>(key: K): void {
    if (!this.isAvailable) {
      return
    }

    try {
      localStorage.removeItem(key)
    } catch (error) {
      console.error(`移除数据失败 [${key}]:`, error)
    }
  }

  /**
   * 清除所有存储数据
   */
  clear(): void {
    if (!this.isAvailable) {
      return
    }

    try {
      localStorage.clear()
    } catch (error) {
      console.error('清除所有数据失败:', error)
    }
  }

  /**
   * 检查key是否存在
   */
  hasItem<K extends keyof StorageValues>(key: K): boolean {
    return this.getItem(key) !== null
  }
}

const storage = new StorageManager()

/**
 * Token管理工具
 */
export class TokenManager {

  /**
   * 保存登录信息
   */
  static async saveLoginData(data: AuthState): Promise<void> {
    storage.setItem(STORAGE_KEYS.ACCESS_TOKEN, data.accessToken)
    storage.setItem(STORAGE_KEYS.REFRESH_TOKEN, data.refreshToken)
    storage.setItem(STORAGE_KEYS.USER_ID, data.userId)
    storage.setItem(STORAGE_KEYS.TOKEN_EXPIRES_TIME, data.expiresTime)

    // 同步到Node端
    await ipcRequestStateSync.onUserLogin()
  }

  /**
   * 获取访问令牌
   */
  static getAccessToken(): string | null {
    return storage.getItem(STORAGE_KEYS.ACCESS_TOKEN)
  }

  /**
   * 获取刷新令牌
   */
  static getRefreshToken(): string | null {
    return storage.getItem(STORAGE_KEYS.REFRESH_TOKEN)
  }

  /**
   * 获取用户ID
   */
  static getUserId(): number | null {
    return storage.getItem(STORAGE_KEYS.USER_ID)
  }

  /**
   * 获取token过期时间
   */
  static getTokenExpiresTime(): number | null {
    return storage.getItem(STORAGE_KEYS.TOKEN_EXPIRES_TIME)
  }

  /**
   * 检查token是否存在
   */
  static hasToken(): boolean {
    return storage.hasItem(STORAGE_KEYS.ACCESS_TOKEN)
  }

  /**
   * 检查token是否过期
   */
  static isTokenExpired(): boolean {
    const expiresTime = TokenManager.getTokenExpiresTime()
    if (!expiresTime) {
      return true
    }
    return Date.now() > expiresTime
  }

  /**
   * 检查用户是否已登录（token存在且未过期）
   */
  static isLoggedIn(): boolean {
    return TokenManager.hasToken() && !TokenManager.isTokenExpired() && TeamManager.current() !== null
  }

  /**
   * 清除所有认证信息
   */
  static async clearAuthData(): Promise<void> {
    storage.removeItem(STORAGE_KEYS.ACCESS_TOKEN)
    storage.removeItem(STORAGE_KEYS.REFRESH_TOKEN)
    storage.removeItem(STORAGE_KEYS.USER_ID)
    storage.removeItem(STORAGE_KEYS.TOKEN_EXPIRES_TIME)

    // 同步到Node端
    await ipcRequestStateSync.onUserLogout()
  }

  /**
   * 获取格式化的Authorization header值
   */
  static getAuthorizationHeader(): string | null {
    const token = TokenManager.getAccessToken()
    return token ? `Bearer ${token}` : null
  }

  /**
   * 获取完整的认证数据
   */
  static getAuthData(): AuthState | null {
    const accessToken = TokenManager.getAccessToken()
    const refreshToken = TokenManager.getRefreshToken()
    const userId = TokenManager.getUserId()
    const expiresTime = TokenManager.getTokenExpiresTime()

    if (!accessToken || !userId) {
      return null
    }

    return {
      accessToken,
      refreshToken: refreshToken || '',
      userId,
      expiresTime: expiresTime || 0,
      openid: '' // 如果需要可以添加openid存储
    }
  }

  /**
   * 刷新token后更新状态
   */
  static async refreshToken(newTokenData: Partial<AuthState>): Promise<void> {
    if (newTokenData.accessToken) {
      storage.setItem(STORAGE_KEYS.ACCESS_TOKEN, newTokenData.accessToken)
    }
    if (newTokenData.refreshToken) {
      storage.setItem(STORAGE_KEYS.REFRESH_TOKEN, newTokenData.refreshToken)
    }
    if (newTokenData.expiresTime) {
      storage.setItem(STORAGE_KEYS.TOKEN_EXPIRES_TIME, newTokenData.expiresTime)
    }

    // 同步到Node端
    await ipcRequestStateSync.onTokenRefresh()
  }
}

/**
 * 租户管理
 */
export class TeamManager {

  static async switch(id: number): Promise<void> {
    storage.setItem(STORAGE_KEYS.TENANT_ID, id)

    // 同步到Node端
    await ipcRequestStateSync.onTeamSwitch(id)
  }

  static async clear(): Promise<void> {
    storage.removeItem(STORAGE_KEYS.TENANT_ID)

    // 同步到Node端
    await ipcRequestStateSync.onTeamSwitch(undefined)
  }

  static current(): number | null {
    return storage.getItem(STORAGE_KEYS.TENANT_ID)
  }
}


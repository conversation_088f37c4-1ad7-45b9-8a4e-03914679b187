import React from 'react'

export interface PendingHook {
  pending: boolean
  withPending: <T extends (...args: any[]) => any>(
    fn: T,
  ) => (...args: Parameters<T>) => Promise<Awaited<ReturnType<T>> | void>
}

export function usePending(): PendingHook {
  const [pending, setPending] = React.useState(false)

  const withPending: PendingHook['withPending'] = React.useCallback(fn => {
    return async (...args: Parameters<typeof fn>) => {
      setPending(true)
      try {
        await fn(...args)
      } finally {
        setPending(false)
      }
    }
  }, [])

  return { pending, withPending }
}

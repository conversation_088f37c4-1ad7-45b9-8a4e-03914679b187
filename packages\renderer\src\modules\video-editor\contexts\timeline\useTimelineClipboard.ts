import { useCallback, useState } from 'react'
import { Overlay } from '@clipnest/remotion-shared/types'
import { cloneDeep } from 'lodash'
import { useEditorContext } from '@/modules/video-editor/contexts'
import { calculateTrackAfterOverlayPushed } from '../../utils/track-helper'

export type TimelineClipboard = {
  /** 当前剪贴板中的 overlay */
  value: Overlay | null

  /** 剪贴当前 Overlay 到剪贴板 */
  clipOverlay(overlay: Overlay): void

  /** 剪贴当前 Overlay 到剪贴板 */
  copyOverlay(overlay: Overlay): void

  /** 清空剪贴板 */
  clearClipboard(): void

  /**
   * 在指定轨道的指定位置粘贴 overlay
   */
  pasteOverlay(trackIndex: number, startFrame: number): void
}

/**
 * Overlay 剪贴板 Hook
 * 提供复制、粘贴 overlay 的功能
 *
 * @returns OverlayClipboard 对象，包含剪贴板状态和操作方法
 *
 * @example
 * ```tsx
 * const clipboard = useOverlayClipboard()
 *
 * // 复制 overlay
 * clipboard.clipOverlay(selectedOverlay)
 *
 * ```
 */
export const useTimelineClipboard = (): TimelineClipboard => {
  const { updateTracks } = useEditorContext()

  // 剪贴板状态 - 存储被复制的 overlay
  const [value, setValue] = useState<Overlay | null>(null)
  const [from, setFrom] = useState<'clip' | 'copy'>()

  const clipOverlay = (overlay: Overlay) => {
    const clippedOverlay = cloneDeep(overlay)
    setValue(clippedOverlay)
    setFrom('clip')
  }

  const copyOverlay = (overlay: Overlay) => {
    const clippedOverlay = cloneDeep(overlay)
    setValue(clippedOverlay)
    setFrom('copy')
  }

  /**
   * 清空剪贴板
   */
  const clearClipboard = () => setValue(null)

  const pasteOverlay = useCallback(
    (trackIndex: number, startFrame: number) => {
      if (!value) return

      updateTracks(prevTracks => (
        prevTracks.map((track, index) => {
          if (from === 'clip' && track.overlays.some(o => o.id === value.id)) {
            return {
              ...track,
              overlays: track.overlays.filter(o => o.id !== value.id)
            }
          }

          if (index === trackIndex) {
            const [newTrack] = calculateTrackAfterOverlayPushed(prevTracks, trackIndex, value, startFrame)
            return newTrack
          }

          return track
        })
      ))

      if (from === 'clip') setValue(null)
    },
    [value, from]
  )

  return {
    value,
    clipOverlay,
    copyOverlay,
    clearClipboard,
    pasteOverlay
  }
}

import Database from 'better-sqlite3'
import { BaseEntity, PaginatedResult, PaginationParams, QueryConditions } from '@app/shared/types/database.types.js'
import { NestDatabaseService } from '@/modules/global/database.service.js'

/**
 * 仓储错误类
 */
export class RepositoryError extends Error {

  constructor(message: string, public readonly cause?: unknown) {
    super(message)
    this.name = 'RepositoryError'
  }
}

/**
 * 通用分页查询结果
 */

/**
 * 通用基础仓储类
 * 提供基本的CRUD操作和查询功能
 *
 * @template T 领域模型类型，必须扩展BaseEntity
 * @template CreateParams 创建参数类型
 * @template UpdateParams 更新参数类型
 * @template QueryParams 查询参数类型，默认为QueryConditions
 */
export abstract class BaseRepository<
  T extends BaseEntity,
  CreateParams = Partial<T>,
  UpdateParams = Partial<T>,
  QueryParams = QueryConditions
> {

  /**
     * 表名
     */
  protected abstract readonly tableName: string

  /**
     * 主键字段名
     */
  protected abstract readonly primaryKey: string

  /**
     * 可排序字段列表
     */
  protected abstract readonly sortableFields: string[]

  /**
     * 可查询字段列表
     */
  protected abstract readonly queryableFields: string[]

  /**
     * 软删除字段名
     */
  protected readonly softDeleteField = 'deleted_at'

  /**
     * 更新时间字段名
     */
  protected readonly updatedAtField = 'updated_at'

  /**
     * 创建时间字段名
     */
  protected readonly createdAtField = 'created_at'

  /**
     * 构造函数
     * @param dbService 数据库服务实例
     */
  constructor(protected dbService: NestDatabaseService) {
    if (!dbService) {
      throw new RepositoryError('数据库服务不能为空')
    }
  }

  /**
     * 获取数据库实例
     */
  protected get db(): Database.Database {
    try {
      return this.dbService.db
    }
    catch (error) {
      throw new RepositoryError('获取数据库实例失败', error)
    }
  }

  /**
   * 将数据库记录转换为领域模型
   * 子类需要实现此方法以提供具体的转换逻辑
   * @param data 数据库记录
   * @returns 领域模型或null
   */
  protected abstract toModel(data: Record<string, any> | null): T | null

  /**
   * 将多个数据库记录转换为领域模型数组
   * @param dataArray 数据库记录数组
   * @returns 领域模型数组
   */
  protected toModelArray(dataArray: Record<string, any>[]): T[] {
    return dataArray.map(data => {
      const model = this.toModel(data)
      if (!model) {
        throw new RepositoryError(`无法将数据转换为模型: ${JSON.stringify(data)}`)
      }
      return model
    })
  }

  /**
     * 创建记录
     * @param params 创建参数
     * @returns 创建的记录
     */
  async create(params: CreateParams): Promise<T> {
    if (!params) {
      throw new RepositoryError('创建参数不能为空')
    }

    try {
      const now = Date.now()
      const data = this.prepareCreateData(params, now)

      if (Object.keys(data).length === 0) {
        throw new RepositoryError('创建数据为空')
      }

      const fields = Object.keys(data)
      const placeholders = fields.map(field => `@${field}`).join(', ')

      const sql = `
                INSERT INTO ${this.tableName} (${fields.join(', ')})
                VALUES (${placeholders})
            `

      const stmt = this.db.prepare(sql)
      const info = stmt.run(data)

      const id = this.getInsertedId(info)
      const result = this.findById(id)

      if (!result) {
        throw new RepositoryError(`创建记录后未找到: ID=${id}`)
      }

      return result
    }
    catch (error) {
      if (error instanceof RepositoryError) {
        throw error
      }
      throw new RepositoryError(`创建${this.tableName}记录失败`, error)
    }
  }

  /**
     * 准备创建数据
     * @param params 创建参数
     * @param timestamp 时间戳
     * @returns 处理后的数据
     */
  protected prepareCreateData(params: CreateParams, timestamp: number): Record<string, any> {
    if (!params) {
      return {}
    }

    const data: Record<string, any> = { ...params as object }

    // 添加时间戳
    if (this.createdAtField && !data[this.createdAtField]) {
      data[this.createdAtField] = timestamp
    }

    if (this.updatedAtField && !data[this.updatedAtField]) {
      data[this.updatedAtField] = timestamp
    }

    // 设置软删除字段默认值
    if (this.softDeleteField && data[this.softDeleteField] === undefined) {
      data[this.softDeleteField] = 0
    }

    return data
  }

  /**
     * 获取插入记录的ID
     * @param info 插入结果信息
     * @returns 插入记录的ID
     */
  protected getInsertedId(info: Database.RunResult): any {
    if (!info) {
      throw new RepositoryError('插入结果信息不能为空')
    }
    return info.lastInsertRowid
  }

  /**
     * 根据ID查找记录
     * @param id 记录ID
     * @param includeDeleted 是否包含已删除记录
     * @returns 记录或null
     */
  findById(id: any, includeDeleted = false): T | null {
    if (id === undefined || id === null) {
      throw new RepositoryError('记录ID不能为空')
    }

    try {
      let sql = `SELECT * FROM ${this.tableName} WHERE ${this.primaryKey} = ?`

      if (!includeDeleted && this.softDeleteField) {
        sql += ` AND ${this.softDeleteField} = 0`
      }

      const stmt = this.db.prepare(sql)
      const data = stmt.get(id) as Record<string, any> | null
      return this.toModel(data)
    }
    catch (error) {
      throw new RepositoryError(`查询${this.tableName}记录失败: ID=${id}`, error)
    }
  }

  /**
     * 查询记录列表
     * @param conditions 查询条件
     * @param pagination 分页参数
     * @returns 分页查询结果
     */
  findAll(conditions: QueryParams = {} as QueryParams, pagination: PaginationParams = {}): PaginatedResult<T> {
    try {
      const { whereClause, params } = this.buildWhereClause(conditions as unknown as QueryConditions)

      // 分页参数
      const page = pagination.pageNo ?? 1
      const pageSize = pagination.pageSize ?? 20

      if (page < 1) {
        throw new RepositoryError('页码必须大于0')
      }

      if (pageSize < 1) {
        throw new RepositoryError('每页记录数必须大于0')
      }

      const offset = (page - 1) * pageSize

      // 排序
      const sortBy = this.validateOrderBy(pagination.sortBy)
      const order = pagination.order?.toLowerCase() === 'asc' ? 'ASC' : 'DESC'

      // 查询总数
      const countSql = `SELECT COUNT(*) as total FROM ${this.tableName} ${whereClause}`
      const countStmt = this.db.prepare(countSql)
      const { total } = countStmt.get(params) as { total: number }

      // 查询数据
      const sql = `
                SELECT * FROM ${this.tableName}
                ${whereClause}
                ORDER BY ${sortBy} ${order}
                LIMIT @limit OFFSET @offset
            `

      const stmt = this.db.prepare(sql)
      const rawItems = stmt.all({
        ...params,
        limit: pageSize,
        offset,
      }) as Record<string, any>[]

      const items = this.toModelArray(rawItems)

      return {
        list: items,
        total,
      }
    }
    catch (error) {
      if (error instanceof RepositoryError) {
        throw error
      }
      throw new RepositoryError(`查询${this.tableName}列表失败`, error)
    }
  }

  /**
   * 批量移动记录到指定父级
   * @param ids 记录ID数组
   * @param parentField 父级字段名
   * @param parentId 目标父级ID
   * @returns 移动的记录数
   */
  public batchMoveToParent(ids: any[], parentField: string, parentId: any): number {
    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return 0
    }

    try {
      const now = Date.now()

      // 开始事务
      const transaction = this.db.transaction(() => {
        const sql = `
          UPDATE ${this.tableName}
          SET ${parentField} = ?, ${this.updatedAtField} = ?
          WHERE ${this.primaryKey} = ? AND ${this.softDeleteField} = 0
        `

        const stmt = this.db.prepare(sql)

        let count = 0
        for (const id of ids) {
          if (id === undefined || id === null) {
            continue
          }
          const result = stmt.run(parentId, now, id)
          count += result.changes
        }

        return count
      })

      return transaction()
    } catch (error) {
      throw new RepositoryError(`批量移动${this.tableName}记录失败`, error)
    }
  }

  /**
   * 通用搜索方法
   * @param keyword 搜索关键词
   * @param searchFields 要搜索的字段列表
   * @param conditions 附加条件
   * @param orderBy 排序字段，默认为创建时间降序
   * @param limit 限制结果数量，默认为100
   * @returns 搜索结果
   */
  protected searchByFields(
    keyword: string,
    searchFields: string[],
    conditions: Record<string, any> = {},
    orderBy: string = 'created_at DESC',
    limit: number = 100
  ): T[] {
    try {
      if (!keyword || searchFields.length === 0) {
        return []
      }

      const searchPattern = `%${keyword}%`
      const whereConditions: string[] = []
      const params: Record<string, any> = { ...conditions }

      // 构建搜索条件
      const searchConditions = searchFields.map((field, index) => {
        const paramName = `search${index}`
        params[paramName] = searchPattern
        return `${field} LIKE @${paramName}`
      })

      // 添加搜索条件
      whereConditions.push(`(${searchConditions.join(' OR ')})`)

      // 添加软删除条件
      if (this.softDeleteField && params[this.softDeleteField] === undefined) {
        whereConditions.push(`${this.softDeleteField} = 0`)
      }

      // 添加其他条件
      for (const [key, value] of Object.entries(conditions)) {
        if (value !== undefined) {
          whereConditions.push(`${key} = @${key}`)
        }
      }

      const whereClause = whereConditions.length > 0
        ? `WHERE ${whereConditions.join(' AND ')}`
        : ''

      const sql = `
        SELECT * FROM ${this.tableName}
        ${whereClause}
        ORDER BY ${orderBy}
        LIMIT ${limit}
      `

      const stmt = this.db.prepare(sql)
      const data = stmt.all(params) as Record<string, any>[]
      return this.toModelArray(data)
    } catch (error) {
      throw new RepositoryError(`搜索${this.tableName}失败`, error)
    }
  }

  /**
     * 验证排序字段
     * @param sortBy 排序字段
     * @returns 验证后的排序字段
     */
  protected validateOrderBy(sortBy?: string): string {
    if (!sortBy || !this.sortableFields.includes(sortBy)) {
      return this.primaryKey
    }
    return sortBy
  }

  /**
     * 构建WHERE子句
     * @param conditions 查询条件
     * @returns WHERE子句和参数
     */
  protected buildWhereClause(conditions: QueryConditions): { whereClause: string, params: Record<string, any> } {
    const clauses: string[] = []
    const params: Record<string, any> = {}

    // 添加软删除条件
    if (this.softDeleteField && conditions[this.softDeleteField] === undefined) {
      clauses.push(`${this.softDeleteField} = 0`)
    }

    // 添加其他条件
    for (const [key, value] of Object.entries(conditions)) {
      if (this.queryableFields.includes(key) && value !== undefined) {
        clauses.push(`${key} = @${key}`)
        params[key] = value
      }
    }

    const whereClause = clauses.length > 0 ? `WHERE ${clauses.join(' AND ')}` : ''
    return { whereClause, params }
  }

  /**
     * 更新记录
     * @param id 记录ID
     * @param params 更新参数
     * @returns 是否更新成功
     */
  update(id: any, params: UpdateParams): boolean {
    if (id === undefined || id === null) {
      throw new RepositoryError('记录ID不能为空')
    }

    if (!params) {
      throw new RepositoryError('更新参数不能为空')
    }

    try {
      const now = Date.now()
      const data = this.prepareUpdateData(params, now)

      if (Object.keys(data).length === 0) {
        return false
      }

      const setClause = Object.keys(data)
        .map(field => `${field} = @${field}`)
        .join(', ')

      const sql = `
                UPDATE ${this.tableName}
                SET ${setClause}
                WHERE ${this.primaryKey} = @id
                ${this.softDeleteField ? `AND ${this.softDeleteField} = 0` : ''}
            `

      const stmt = this.db.prepare(sql)
      const result = stmt.run({
        ...data,
        id,
      })

      return result.changes > 0
    }
    catch (error) {
      throw new RepositoryError(`更新${this.tableName}记录失败: ID=${id}`, error)
    }
  }

  /**
   * 更新记录状态
   * @param id 记录ID
   * @param statusField 状态字段名
   * @param statusValue 状态值
   * @param additionalFields 额外需要更新的字段
   * @returns 是否更新成功
   */
  public updateStatus(
    id: any,
    statusField: string,
    statusValue: any,
    additionalFields: Record<string, any> = {}
  ): boolean {
    try {
      const now = Date.now()
      const data: Record<string, any> = {
        [statusField]: statusValue,
        [this.updatedAtField]: now,
        ...additionalFields
      }

      const fields = Object.keys(data)
      const setClause = fields.map(field => `${field} = @${field}`).join(', ')

      const sql = `
        UPDATE ${this.tableName}
        SET ${setClause}
        WHERE ${this.primaryKey} = @id
        AND ${this.softDeleteField} = 0
      `

      const stmt = this.db.prepare(sql)
      const info = stmt.run({
        ...data,
        id
      })

      return info.changes > 0
    } catch (error) {
      throw new RepositoryError(`更新${this.tableName}状态失败: ID=${id}`, error)
    }
  }

  /**
     * 准备更新数据
     * @param params 更新参数
     * @param timestamp 时间戳
     * @returns 处理后的数据
     */
  protected prepareUpdateData(params: UpdateParams, timestamp: number): Record<string, any> {
    if (!params) {
      return {}
    }

    const data: Record<string, any> = { ...params as object }

    // 添加更新时间戳
    if (this.updatedAtField && !data[this.updatedAtField]) {
      data[this.updatedAtField] = timestamp
    }

    // 移除主键和创建时间字段
    delete data[this.primaryKey]
    delete data[this.createdAtField]

    return data
  }

  /**
     * 软删除记录
     * @param id 记录ID
     * @returns 是否删除成功
     */
  softDelete(id: any): boolean {
    if (id === undefined || id === null) {
      throw new RepositoryError('记录ID不能为空')
    }

    if (!this.softDeleteField) {
      throw new RepositoryError('软删除功能未启用')
    }

    try {
      const now = Date.now()
      const data: Record<string, any> = {
        [this.softDeleteField]: now,
      }

      // 自动添加更新时间
      if (this.updatedAtField) {
        data[this.updatedAtField] = now
      }

      const setClause = Object.keys(data)
        .map(field => `${field} = @${field}`)
        .join(', ')

      const sql = `
                UPDATE ${this.tableName}
                SET ${setClause}
                WHERE ${this.primaryKey} = @id
                AND ${this.softDeleteField} = 0
            `

      const stmt = this.db.prepare(sql)
      const result = stmt.run({
        ...data,
        id,
      })

      return result.changes > 0
    }
    catch (error) {
      throw new RepositoryError(`软删除${this.tableName}记录失败: ID=${id}`, error)
    }
  }

  /**
     * 恢复软删除的记录
     * @param id 记录ID
     * @returns 是否恢复成功
     */
  restore(id: any): boolean {
    if (id === undefined || id === null) {
      throw new RepositoryError('记录ID不能为空')
    }

    if (!this.softDeleteField) {
      throw new RepositoryError('软删除功能未启用')
    }

    try {
      const now = Date.now()
      const data: Record<string, any> = {
        [this.softDeleteField]: 0,
      }

      // 自动添加更新时间
      if (this.updatedAtField) {
        data[this.updatedAtField] = now
      }

      const setClause = Object.keys(data)
        .map(field => `${field} = @${field}`)
        .join(', ')

      const sql = `
                UPDATE ${this.tableName}
                SET ${setClause}
                WHERE ${this.primaryKey} = @id
                AND ${this.softDeleteField} > 0
            `

      const stmt = this.db.prepare(sql)
      const result = stmt.run({
        ...data,
        id,
      })

      return result.changes > 0
    }
    catch (error) {
      throw new RepositoryError(`恢复${this.tableName}记录失败: ID=${id}`, error)
    }
  }

  /**
     * 物理删除记录
     * @param id 记录ID
     * @returns 是否删除成功
     */
  hardDelete(id: any): boolean {
    if (id === undefined || id === null) {
      throw new RepositoryError('记录ID不能为空')
    }

    try {
      const sql = `
                DELETE FROM ${this.tableName}
                WHERE ${this.primaryKey} = ?
            `

      const stmt = this.db.prepare(sql)
      const result = stmt.run(id)

      return result.changes > 0
    }
    catch (error) {
      throw new RepositoryError(`物理删除${this.tableName}记录失败: ID=${id}`, error)
    }
  }

  /**
     * 批量软删除记录
     * @param ids 记录ID数组
     * @returns 删除的记录数
     */
  batchSoftDelete(ids: any[]): number {
    if (!ids || !Array.isArray(ids)) {
      throw new RepositoryError('记录ID数组不能为空')
    }

    if (!this.softDeleteField) {
      throw new RepositoryError('软删除功能未启用')
    }

    if (ids.length === 0) {
      return 0
    }

    try {
      const now = Date.now()

      // 开始事务
      const transaction = this.db.transaction(() => {
        const sql = `
                    UPDATE ${this.tableName}
                    SET ${this.softDeleteField} = ?, ${this.updatedAtField} = ?
                    WHERE ${this.primaryKey} = ? AND ${this.softDeleteField} = 0
                `

        const stmt = this.db.prepare(sql)

        let count = 0
        for (const id of ids) {
          if (id === undefined || id === null) {
            continue
          }
          const result = stmt.run(now, now, id)
          count += result.changes
        }

        return count
      })

      return transaction()
    }
    catch (error) {
      throw new RepositoryError(`批量软删除${this.tableName}记录失败`, error)
    }
  }

  /**
     * 批量物理删除记录
     * @param ids 记录ID数组
     * @returns 删除的记录数
     */
  batchHardDelete(ids: any[]): number {
    if (!ids || !Array.isArray(ids)) {
      throw new RepositoryError('记录ID数组不能为空')
    }

    if (ids.length === 0) {
      return 0
    }

    try {
      // 开始事务
      const transaction = this.db.transaction(() => {
        const sql = `
                    DELETE FROM ${this.tableName}
                    WHERE ${this.primaryKey} = ?
                `

        const stmt = this.db.prepare(sql)

        let count = 0
        for (const id of ids) {
          if (id === undefined || id === null) {
            continue
          }
          const result = stmt.run(id)
          count += result.changes
        }

        return count
      })

      return transaction()
    }
    catch (error) {
      throw new RepositoryError(`批量物理删除${this.tableName}记录失败`, error)
    }
  }
}

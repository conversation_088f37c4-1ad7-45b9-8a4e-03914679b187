import React, { useState, useMemo, forwardRef, useImperative<PERSON><PERSON>le, useEffect } from 'react'
import { ColumnDef } from '@tanstack/react-table'
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
} from '@/components/ui/drawer'
import { DataTable } from '@/components/ui/data-table'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { SearchIcon, X, ChevronRight } from 'lucide-react'
import { VideoModule } from '@/libs/request/api/video'
import { CreativeVideo, CreativeVideoRequestParams } from '@/types/video'

import { usePagination } from '@/hooks/usePagination'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { formatTimestamp } from '@/components/lib/utils'
import { Checkbox } from '@/components/ui/checkbox'
import { ScrollArea } from '@/components/ui/scroll-area'
import { useInfiniteQueryProjectList } from '@/hooks/queries/useQueryProject'
import { Project } from '@/types/project'
import { SimpleVideo } from '../publish/components/VideoSelectionSection'

export interface VideoSelectDrawerRef {
  open: (defaultSelectedVideos?: SimpleVideo[], singleSelectMode?: boolean, excludeVideos?: SimpleVideo[]) => void
  close: () => void
}

interface VideoSelectDrawerProps {
  onVideoSelect?: (videos: SimpleVideo[]) => void
}

export const VideoSelectDrawer = forwardRef<VideoSelectDrawerRef, VideoSelectDrawerProps>(
  ({ onVideoSelect }, ref) => {
    const [open, setOpen] = useState(false)
    const [searchTerm, setSearchTerm] = useState('')
    const [statusFilter, setStatusFilter] = useState<string>('all')
    const [repetitionRateFilter, setRepetitionRateFilter] = useState<string>('all')
    const [selectedVideos, setSelectedVideos] = useState<SimpleVideo[]>([])
    const [selectedVideoUrls, setSelectedVideoUrls] = useState<Set<string>>(new Set())
    const [singleSelectMode, setSingleSelectMode] = useState(false)
    const [excludeVideoUrls, setExcludeVideoUrls] = useState<Set<string>>(new Set())

    // 项目相关状态
    const [projectSearchTerm, setProjectSearchTerm] = useState('')
    const [selectedProject, setSelectedProject] = useState<Project | null>(null)

    const {
      data: projectData,
      fetchNextPage: fetchNextProjectPage,
      hasNextPage: hasNextProjectPage,
      isFetchingNextPage: isFetchingNextProjectPage,
      isLoading: isLoadingProjects
    } = useInfiniteQueryProjectList({
      keyword: projectSearchTerm.trim() || undefined,
      pageSize: 20
    })

    // 扁平化项目列表
    const projectList = useMemo(() => {
      return projectData?.pages.flatMap(page => page.list) || []
    }, [projectData])

    // 构建搜索参数
    const searchParams = useMemo(() => {
      const params: CreativeVideoRequestParams = {}

      if (selectedProject) {
        params.projectId = selectedProject.id.toString()
      }

      if (searchTerm.trim()) {
        params.keyword = searchTerm.trim()
      }

      if (statusFilter !== 'all') {
        params.composeStatus = statusFilter
      }

      if (repetitionRateFilter !== 'all') {
        params.repetitionRateRange = repetitionRateFilter
      }

      return params
    }, [selectedProject, searchTerm, statusFilter, repetitionRateFilter])

    const {
      data: videoList,
      pagination,
      isLoading,
      isError,
      setPagination,
    } = usePagination<CreativeVideo, CreativeVideoRequestParams>({
      queryKey: [QUERY_KEYS.REMIX_PREVIEW_PAGE],
      queryFn: VideoModule.videoPage,
      searchParams,
      initialPageSize: 20,
      enabled: open && !!selectedProject, // 只有选中项目时才启用查询
    })

    useEffect(() => {
      if (open && projectList.length > 0 && !selectedProject) {
        setSelectedProject(projectList[0])
      }
    }, [open, projectList, selectedProject])

    useImperativeHandle(ref, () => ({
      open: (defaultSelectedVideos?: SimpleVideo[], singleMode?: boolean, excludeVideos?: SimpleVideo[]) => {
        setSearchTerm('')
        setStatusFilter('all')
        setRepetitionRateFilter('all')
        setSingleSelectMode(singleMode || false)
        setSelectedVideos(defaultSelectedVideos || [])
        setSelectedVideoUrls(new Set((defaultSelectedVideos || []).map(v => v.url)))
        setExcludeVideoUrls(new Set((excludeVideos || []).map(v => v.url)))
        setProjectSearchTerm('')
        setSelectedProject(null) // 这里设为null，让useEffect自动选中第一个项目
        setPagination({ pageIndex: 0, pageSize: 20 })
        setOpen(true)
      },
      close: () => {
        setOpen(false)
        setSearchTerm('')
        setStatusFilter('all')
        setRepetitionRateFilter('all')
        setSingleSelectMode(false)
        setSelectedVideos([])
        setSelectedVideoUrls(new Set())
        setExcludeVideoUrls(new Set())
        setProjectSearchTerm('')
        setSelectedProject(null)
        setPagination({ pageIndex: 0, pageSize: 20 })
      }
    }), [setPagination])

    // 处理项目滚动到底部加载更多
    const handleProjectScroll = (event: React.UIEvent<HTMLDivElement>) => {
      const { scrollTop, scrollHeight, clientHeight } = event.currentTarget
      const threshold = 50 // 距离底部50px时触发加载

      if (scrollHeight - scrollTop - clientHeight < threshold && hasNextProjectPage && !isFetchingNextProjectPage) {
        fetchNextProjectPage()
      }
    }

    // 处理项目选择
    const handleProjectSelect = (project: Project) => {
      setSelectedProject(project)
      // 不清空已选择的视频，保持用户的选择状态
      setPagination({ pageIndex: 0, pageSize: 20 }) // 重置分页
    }

    // 处理视频选择
    const handleVideoToggle = (video: CreativeVideo, checked: boolean) => {
      // 在单选模式下，如果视频被排除，则不允许选择
      if (singleSelectMode && checked && excludeVideoUrls.has(video.url)) {
        return
      }

      const simpleVideo: SimpleVideo = {
        url: video.url,
        cover: video.cover,
        name: video.name
      }

      if (singleSelectMode) {
        // 单选模式：只能选择一个视频
        if (checked) {
          setSelectedVideos([simpleVideo])
          setSelectedVideoUrls(new Set([video.url]))
        } else {
          setSelectedVideos([])
          setSelectedVideoUrls(new Set())
        }
      } else {
        // 多选模式：原有逻辑
        if (checked) {
          setSelectedVideos(prev => [...prev, simpleVideo])
          setSelectedVideoUrls(prev => new Set([...prev, video.url]))
        } else {
          setSelectedVideos(prev => prev.filter(v => v.url !== video.url))
          setSelectedVideoUrls(prev => {
            const newSet = new Set(prev)
            newSet.delete(video.url)
            return newSet
          })
        }
      }
    }

    // 处理当前页全选/取消全选
    const handleSelectAll = (checked: boolean) => {
      if (checked) {
        // 全选当前页：将当前页的视频添加到已选列表中
        const currentPageVideos = (videoList || []).map(video => ({
          url: video.url,
          cover: video.cover,
          name: video.name
        }))

        // 合并已选视频和当前页视频，去重
        const allSelectedVideos = [...selectedVideos]
        const allSelectedUrls = new Set(selectedVideoUrls)

        currentPageVideos.forEach(video => {
          if (!allSelectedUrls.has(video.url)) {
            allSelectedVideos.push(video)
            allSelectedUrls.add(video.url)
          }
        })

        setSelectedVideos(allSelectedVideos)
        setSelectedVideoUrls(allSelectedUrls)
      } else {
        // 取消全选当前页：从已选列表中移除当前页的视频
        const currentPageUrls = new Set((videoList || []).map(v => v.url))
        const filteredVideos = selectedVideos.filter(v => !currentPageUrls.has(v.url))
        const filteredUrls = new Set(filteredVideos.map(v => v.url))

        setSelectedVideos(filteredVideos)
        setSelectedVideoUrls(filteredUrls)
      }
    }

    // 移除单个已选择的视频
    const handleRemoveSelectedVideo = (videoUrl: string) => {
      setSelectedVideos(prev => prev.filter(v => v.url !== videoUrl))
      setSelectedVideoUrls(prev => {
        const newSet = new Set(prev)
        newSet.delete(videoUrl)
        return newSet
      })
    }

    // 确认选择
    const handleConfirmSelect = () => {
      onVideoSelect?.(selectedVideos)
      setOpen(false)
    }

    // 检查当前页是否全选
    const isCurrentPageAllSelected = useMemo(() => {
      if (!videoList || videoList.length === 0) return false
      return videoList.every(video => selectedVideoUrls.has(video.url))
    }, [videoList, selectedVideoUrls])

    // 检查当前页是否部分选中
    const isCurrentPageIndeterminate = useMemo(() => {
      if (!videoList || videoList.length === 0) return false
      const selectedCount = videoList.filter(video => selectedVideoUrls.has(video.url)).length
      return selectedCount > 0 && selectedCount < videoList.length
    }, [videoList, selectedVideoUrls])

    // 表格列定义
    const columns: ColumnDef<CreativeVideo>[] = [
      {
        id: 'select',
        header: () => (
          singleSelectMode ? (
            // 单选模式下显示空白或者"选择"文字
            <span className="text-sm text-muted-foreground">选择</span>
          ) : (
            // 多选模式下显示全选复选框
            <Checkbox
              checked={isCurrentPageAllSelected}
              ref={el => {
                if (el) {
                  const checkbox = el.querySelector('input[type="checkbox"]') as HTMLInputElement
                  if (checkbox) {
                    checkbox.indeterminate = isCurrentPageIndeterminate
                  }
                }
              }}
              onCheckedChange={value => {
                handleSelectAll(!!value)
              }}
              aria-label="Select all"
            />
          )
        ),
        cell: ({ row }) => {
          const video = row.original
          const isSelected = selectedVideoUrls.has(video.url)
          const isExcluded = singleSelectMode && excludeVideoUrls.has(video.url)

          if (singleSelectMode) {
            // 单选模式使用单选按钮
            return (
              <div className="flex items-center">
                <input
                  type="radio"
                  checked={isSelected}
                  disabled={isExcluded}
                  onChange={() => {
                    if (!isExcluded) {
                      handleVideoToggle(video, !isSelected)
                    }
                  }}
                  className={`w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 focus:ring-blue-500 ${
                    isExcluded ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                  aria-label="Select row"
                />
                {isExcluded && (
                  <span className="ml-2 text-xs text-red-500">已选择</span>
                )}
              </div>
            )
          } else {
            // 多选模式使用复选框
            return (
              <Checkbox
                checked={isSelected}
                onCheckedChange={value => {
                  handleVideoToggle(video, !!value)
                }}
                aria-label="Select row"
              />
            )
          }
        },
        enableSorting: false,
        enableHiding: false,
        size: 50,
      },
      {
        id: 'video',
        header: '视频',
        cell: ({ row }) => {
          const item = row.original
          return (
            <div className="flex items-center space-x-3">
              <div className="w-16 h-12 bg-gray-100 rounded overflow-hidden">
                {item.cover ? (
                  <img
                    src={item.cover}
                    alt="视频封面"
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-xs text-gray-400">
                    无封面
                  </div>
                )}
              </div>
            </div>
          )
        },
        size: 80,
      },
      {
        accessorKey: 'name',
        header: '视频名称',
        cell: ({ row }) => {
          const name = row.getValue('name') as string
          const video = row.original
          const isExcluded = singleSelectMode && excludeVideoUrls.has(video.url)
          return (
            <div className="max-w-[200px]">
              <div className={`font-medium truncate ${isExcluded ? 'text-gray-400' : ''}`}>
                {name || '--'}
                {isExcluded && (
                  <span className="ml-2 text-xs text-red-500 bg-red-50 px-2 py-1 rounded">
                    已选择
                  </span>
                )}
              </div>
            </div>
          )
        },
      },
      {
        accessorKey: 'duration',
        header: '时长',
        cell: ({ row }) => {
          const duration = row.getValue('duration') as number
          const minutes = Math.floor(duration / 60)
          const seconds = duration % 60
          return `${minutes}:${seconds.toString().padStart(2, '0')}`
        },
        size: 80,
      },
      {
        accessorKey: 'repetitionRate',
        header: '重复率',
        cell: ({ row }) => {
          const rate = row.getValue('repetitionRate') as number
          return (
            <Badge variant={rate > 80 ? 'destructive' : rate > 50 ? 'secondary' : 'default'}>
              {rate}%
            </Badge>
          )
        },
        size: 80,
      },
      {
        accessorKey: 'createTime',
        header: '创建时间',
        cell: ({ row }) => {
          const createTime = row.getValue('createTime') as number
          return formatTimestamp(createTime)
        },
        size: 150,
      },
    ]

    return (
      <Drawer
        direction="right"
        open={open}
        onOpenChange={setOpen}
      >
        <DrawerContent className="h-screen data-[vaul-drawer-direction=right]:w-[1000px] flex flex-col">
          <DrawerHeader className="border-b flex-shrink-0">
            <div className="flex items-center justify-between">
              <div>
                <DrawerTitle className="flex items-center gap-2">
                  {singleSelectMode ? '更换视频' : '选择视频'}
                  {selectedVideos.length > 0 && (
                    <Badge variant="secondary" className="bg-blue-100 text-blue-700">
                      已选 {selectedVideos.length} 个
                    </Badge>
                  )}
                </DrawerTitle>
                <DrawerDescription>
                  {singleSelectMode
                    ? '从视频库中选择一个视频来替换当前视频。已选择的视频将被禁用，无法重复选择。'
                    : '从视频库中选择要发布的视频，可以跨项目选择，切换项目不会清空已选择的视频'}
                </DrawerDescription>
              </div>
              <DrawerClose asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <X className="h-4 w-4" />
                </Button>
              </DrawerClose>
            </div>
          </DrawerHeader>

          <div className="flex-1 flex overflow-hidden">
            {/* 左侧项目选择器 */}
            <div className="w-56 border-r flex flex-col">
              <div className="p-4 border-b">
                <div className="text-sm font-medium mb-3">选择项目</div>
                <div className="relative">
                  <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="输入关键词搜索"
                    value={projectSearchTerm}
                    onChange={e => setProjectSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <ScrollArea className="flex-1" onScrollCapture={handleProjectScroll}>
                <div className="p-2 flex flex-col gap-2">
                  {isLoadingProjects && projectList.length === 0 && (
                    <div className="flex items-center justify-center py-8 text-sm text-gray-500">
                      加载中...
                    </div>
                  )}

                  {projectList.map(project => (
                    <div
                      key={project.id}
                      className={`flex items-center justify-between p-3 rounded-lg cursor-pointer hover:bg-neutral-900 transition-colors ${
                        selectedProject?.id === project.id ? 'bg-neutral-800' : ''
                      }`}
                      onClick={() => handleProjectSelect(project)}
                    >
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm truncate">{project.projectName}</div>
                        
                      </div>
                      {selectedProject?.id === project.id && (
                        <ChevronRight className="w-4 h-4" />
                      )}
                    </div>
                  ))}

                  {isFetchingNextProjectPage && (
                    <div className="flex items-center justify-center py-4 text-sm text-gray-500">
                      加载更多...
                    </div>
                  )}
                </div>
              </ScrollArea>
            </div>

            {/* 右侧视频列表 */}
            <div className="flex-1 flex flex-col overflow-hidden px-6 mt-3 min-h-0">
              {/* 筛选区域 */}
              <div className="p-4 border-b space-y-3 flex-shrink-0">
                <div className="flex items-center justify-between">
                  <div className="text-sm font-medium">视频列表</div>
                  <div className="text-sm text-muted-foreground">
                    已选择 {selectedVideos.length} 个视频
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  {/* 搜索框 */}
                  <div className="relative flex-1">
                    <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="搜索视频名称"
                      value={searchTerm}
                      onChange={e => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>

                  {/* 状态筛选 */}
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="全部状态" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部状态</SelectItem>
                      <SelectItem value="0">待合成</SelectItem>
                      <SelectItem value="1">合成中</SelectItem>
                      <SelectItem value="2">合成成功</SelectItem>
                      <SelectItem value="3">合成失败</SelectItem>
                    </SelectContent>
                  </Select>

                  {/* 重复率筛选 */}
                  <Select value={repetitionRateFilter} onValueChange={setRepetitionRateFilter}>
                    <SelectTrigger className="w-32">
                      <SelectValue placeholder="重复率" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">全部重复率</SelectItem>
                      <SelectItem value="0-30">0-30%</SelectItem>
                      <SelectItem value="30-50">30-50%</SelectItem>
                      <SelectItem value="50-80">50-80%</SelectItem>
                      <SelectItem value="80-100">80-100%</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* 数据表格 */}
              <div className="flex-1 min-h-0 overflow-hidden">
                {!selectedProject ? (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    <div className="text-center">
                      <div className="text-lg mb-2">请先选择项目</div>
                      <div className="text-sm">从左侧项目列表中选择一个项目来查看视频</div>
                    </div>
                  </div>
                ) : (
                  <DataTable
                    columns={columns}
                    data={videoList || []}
                    pagination={pagination}
                    onPaginationChange={setPagination}
                    loading={isLoading}
                    emptyMessage={isError ? '加载数据失败，请重试' : '暂无视频数据'}
                    className="h-full"
                  />
                )}
              </div>

              {/* 已选择视频展示区域 - 单选模式下隐藏 */}
              {!singleSelectMode && selectedVideos.length > 0 && (
                <div className="rounded-lg bg-neutral-800 p-4 flex-shrink-0">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <span className="text-sm ">
                        已选 {selectedVideos.length} 个
                      </span>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setSelectedVideos([])
                          setSelectedVideoUrls(new Set())
                        }}
                      >
                        清空
                      </Button>
                    </div>
                  </div>

                  {/* 已选择视频列表 */}
                  <ScrollArea className="h-24">
                    <div className="flex flex-wrap gap-3">
                      {selectedVideos.map(video => (
                        <div
                          key={video.url}
                          className="relative group bg-neutral-600 border border-border rounded-lg  shadow-sm hover:shadow-md transition-shadow"
                          style={{ width: '90px' }}
                        >
                          {/* 删除按钮 */}
                          <Button
                            variant="ghost"
                            size="sm"
                            className="absolute top-1 right-1 z-10 h-6 w-6 p-0 bg-blue-500 hover:bg-blue-600 rounded-full opacity-0 group-hover:opacity-100 transition-opacity shadow-md"
                            onClick={() => handleRemoveSelectedVideo(video.url)}
                          >
                            <X className="w-3 h-3" />
                          </Button>

                          {/* 视频封面 */}
                          <div className="w-full h-16 rounded-t-lg bg-neutral-500 overflow-hidden">
                            {video.cover ? (
                              <img
                                src={video.cover}
                                alt="视频封面"
                                className="w-full h-full object-cover"
                              />
                            ) : (
                              <div className="w-full h-full flex items-center justify-center text-xs text-gray-400">
                                无封面
                              </div>
                            )}
                          </div>

                          {/* 视频名称 */}
                          <div className="p-1">
                            <div className="text-xs  truncate mb-1" title={video.name}>
                              {video.name || '未命名'}
                            </div>

                          </div>
                        </div>
                      ))}
                    </div>
                  </ScrollArea>
                </div>
              )}

              {/* 底部操作栏 */}
              <div className="border-t p-4 flex items-center justify-between flex-shrink-0">
                <div className="text-sm text-muted-foreground">
                  {singleSelectMode
                    ? (selectedVideos.length > 0 ? '已选择 1 个视频' : '请选择要替换的视频')
                    : (selectedVideos.length > 0 ? `已选择 ${selectedVideos.length} 个视频` : '请选择视频')}
                </div>
                <div className="flex items-center space-x-3">
                  <Button
                    variant="outline"
                    onClick={() => setOpen(false)}
                    className="px-6"
                  >
                    取消
                  </Button>
                  <Button
                    onClick={handleConfirmSelect}
                    disabled={selectedVideos.length === 0}
                    className="px-6 bg-blue-600 hover:bg-blue-700"
                  >
                    {singleSelectMode ? '确认替换' : '确定'}
                  </Button>
                </div>
              </div>
            </div>
            {/* 右侧视频列表结束 */}
          </div>
          {/* flex容器结束 */}
        </DrawerContent>
      </Drawer>
    )
  }
)

VideoSelectDrawer.displayName = 'VideoSelectDrawer'

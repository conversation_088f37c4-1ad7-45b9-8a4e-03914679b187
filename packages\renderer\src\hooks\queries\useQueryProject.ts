import { PaginationParams } from '@app/shared/types/database.types'
import { QUERY_KEYS } from '../../constants/queryKeys'
import { ResourceModule } from '../../libs/request/api/resource'
import { useInfiniteQuery } from '../useInfiniteQuery'
import { useQuery } from '@tanstack/react-query'

type ProjectQueryParams = PaginationParams & {
  keyword?: string
}

/**
 * 使用无限查询获取项目列表，支持无限滚动加载
 * @param params 查询参数，包括分类ID和每页大小
 * @returns 无限查询结果
 */
export const useInfiniteQueryProjectList = (params: ProjectQueryParams = {}) => {
  return useInfiniteQuery([QUERY_KEYS.PROJECT_PAGE], ResourceModule.project.page, params, {
    pageSize: params.pageSize || 20,
  })
}

export const useQueryProjectList = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.PROJECT_LIST],
    queryFn: () => ResourceModule.project.list(),
  })
}

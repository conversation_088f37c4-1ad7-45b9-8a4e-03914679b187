import { useEffect, useMemo, useRef } from 'react'
import { debounce } from 'lodash'

export function useDebounce(callback: () => void, delay: number, deps: unknown[] = []) {
  const callbackRef = useRef(callback)
  useEffect(() => {
    callbackRef.current = callback
  }, [callback])

  const debounced = useMemo(
    () =>
      debounce(() => {
        callbackRef.current()
      }, delay),
    [delay],
  )

  useEffect(() => {
    debounced()
    return () => {
      // 组件卸载时立即触发挂起的保存，防止丢失
      debounced.flush()
    }
  }, [...deps, debounced])
}

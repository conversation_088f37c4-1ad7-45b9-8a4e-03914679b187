import { ResourceType, ResourceCacheEntry } from '../resource-cache.types.js'

/**
 * 资源缓存客户端接口
 */
export interface ResourceIPCClient {
  /**
   * 获取资源的本地路径，如果不存在则下载并缓存
   * @param params.url CDN URL
   * @param params.type 资源类型
   * @param params.version 资源版本
   * @returns 本地文件路径
   */
  fetchOrSaveResource(params: {
    url: string;
    type: ResourceType;
    version?: string;
    customExt?: string
  }): Promise<string>;

  /**
   * 清理过期或超出大小限制的缓存
   * @param params.maxAgeSeconds 可选，最大缓存时间（秒），默认使用类配置
   * @param params.maxCacheSizeMB 可选，最大缓存大小（MB），默认使用类配置
   */
  cleanResource(params?: { maxAgeSeconds?: number; maxCacheSizeMB?: number }): Promise<void>;

  /**
   * 根据 URL 获取缓存条目
   * @param params.url 资源 URL
   * @returns 缓存条目或 undefined
   */
  getResource(params: { url: string }): Promise<ResourceCacheEntry | undefined>;

  /**
   * 根据 URL 获取本地缓存路径 (不触发下载)
   * @param params.url 资源 URL
   * @param params.type 资源类型
   * @returns 本地文件路径
   */
  getResourcePath(params: { url: string; type: ResourceType }): Promise<string>;

  /**
   * 获取所有已缓存资源的列表
   * @returns 所有缓存条目的数组
   */
  getAllResources(): Promise<ResourceCacheEntry[]>;
}

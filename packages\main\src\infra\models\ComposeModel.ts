import { BaseEntity, Compose } from '@app/shared/types/database.types.js'

/**
 * 合成记录模型类
 * 表示compose表中的一条记录
 */
export class ComposeModel implements BaseEntity, Compose.ICompose {

  id: number
  cid: number
  team_id: number
  script_id: string
  url: string
  path: string
  object_oid: string
  name: string
  cover: string
  duration: number
  width: number
  height: number
  size: number
  status: number
  reason: string
  download_count: number
  download_progress: number
  download_status: number
  download_reason: string
  read: number
  notify: number
  auto_download: number
  auto_download_path: string
  group_At: number | null
  download_at: number | null
  deleted_at: number
  updated_at: number
  created_at: number

  /**
   * 构造函数
   * @param data 初始化数据
   */
  constructor(data?: Partial<ComposeModel>) {
    this.id = data?.id ?? 0
    this.cid = data?.cid ?? 0
    this.team_id = data?.team_id ?? 0
    this.script_id = data?.script_id ?? ''
    this.url = data?.url ?? ''
    this.path = data?.path ?? ''
    this.object_oid = data?.object_oid ?? ''
    this.name = data?.name ?? ''
    this.cover = data?.cover ?? ''
    this.duration = data?.duration ?? 0
    this.width = data?.width ?? 0
    this.height = data?.height ?? 0
    this.size = data?.size ?? 0
    this.status = data?.status ?? Compose.Status.PENDING
    this.reason = data?.reason ?? ''
    this.download_count = data?.download_count ?? 0
    this.download_progress = data?.download_progress ?? 0
    this.download_status = data?.download_status ?? Compose.DownloadStatus.NOT_STARTED
    this.download_reason = data?.download_reason ?? ''
    this.read = data?.read ?? 0
    this.notify = data?.notify ?? 0
    this.auto_download = data?.auto_download ?? 0
    this.auto_download_path = data?.auto_download_path ?? ''
    this.group_At = data?.group_At ?? null
    this.download_at = data?.download_at ?? null
    this.deleted_at = data?.deleted_at ?? 0
    this.updated_at = data?.updated_at ?? Math.floor(Date.now() / 1000)
    this.created_at = data?.created_at ?? Math.floor(Date.now() / 1000)
  }

  /**
   * 合成记录是否已删除
   */
  get isDeleted(): boolean {
    return this.deleted_at > 0
  }

  /**
   * 合成记录是否已完成
   */
  get isCompleted(): boolean {
    return this.status === Compose.Status.COMPLETED
  }

  /**
   * 合成记录是否处理失败
   */
  get isFailed(): boolean {
    return this.status === Compose.Status.FAILED
  }

  /**
   * 合成记录是否已下载
   */
  get isDownloaded(): boolean {
    return this.download_status === Compose.DownloadStatus.COMPLETED &&
           this.path !== '' &&
           this.download_progress === 100
  }

  /**
   * 合成记录是否可下载
   */
  get isDownloadable(): boolean {
    return this.status === Compose.Status.COMPLETED &&
           this.download_status !== Compose.DownloadStatus.DOWNLOADING
  }

  /**
   * 从数据库行记录创建实例
   * @param row 数据库行记录
   * @returns ComposeModel实例
   */
  static fromRow(row: Record<string, any>): ComposeModel {
    return new ComposeModel({
      id: row.id,
      cid: row.cid,
      team_id: row.team_id,
      script_id: row.script_id,
      url: row.url,
      path: row.path,
      object_oid: row.object_oid,
      name: row.name,
      cover: row.cover,
      duration: row.duration,
      width: row.width,
      height: row.height,
      size: row.size,
      status: row.status,
      reason: row.reason,
      download_count: row.download_count,
      download_progress: row.download_progress,
      download_status: row.download_status,
      download_reason: row.download_reason,
      read: row.read,
      notify: row.notify,
      auto_download: row.auto_download,
      auto_download_path: row.auto_download_path,
      group_At: row.group_At,
      download_at: row.download_at,
      deleted_at: row.deleted_at,
      updated_at: row.updated_at,
      created_at: row.created_at
    })
  }

  /**
   * 转换为纯对象
   * @returns 纯对象
   */
  toJSON(): Record<string, any> {
    return {
      id: this.id,
      cid: this.cid,
      team_id: this.team_id,
      script_id: this.script_id,
      url: this.url,
      path: this.path,
      object_oid: this.object_oid,
      name: this.name,
      cover: this.cover,
      duration: this.duration,
      width: this.width,
      height: this.height,
      size: this.size,
      status: this.status,
      reason: this.reason,
      download_count: this.download_count,
      download_progress: this.download_progress,
      download_status: this.download_status,
      download_reason: this.download_reason,
      read: this.read,
      notify: this.notify,
      auto_download: this.auto_download,
      auto_download_path: this.auto_download_path,
      group_At: this.group_At,
      download_at: this.download_at,
      deleted_at: this.deleted_at,
      updated_at: this.updated_at,
      created_at: this.created_at
    }
  }
}

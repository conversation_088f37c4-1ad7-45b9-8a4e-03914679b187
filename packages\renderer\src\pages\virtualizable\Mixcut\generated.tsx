import React, { <PERSON>psWithChildren, useMemo, useState } from 'react'
import { GeneratedMixcut, useMixcutContext } from '@/contexts'
import { OverlayType, VideoOverlay } from '@clipnest/remotion-shared/types'
import { MultiSelectableCard } from '@/pages/virtualizable/Mixcut/common'
import { clsx } from 'clsx'
import { Button } from '@/components/ui/button'
import { List, Play, Plus, Settings, X } from 'lucide-react'
import { useQueryVideoKeyframe } from '@/hooks/queries/useQueryVideoKeyframe'
import { Drawer, DrawerClose, DrawerContent, DrawerHeader, DrawerTitle, DrawerTrigger } from '@/components/ui/drawer'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'

export const VideoPreviewFrame: React.FC<{ overlay?: VideoOverlay }> = ({ overlay }) => {
  const { data: keyframe, isLoading: keyframeLoading } = useQueryVideoKeyframe(overlay?.src)

  return (
    <>
      {keyframe && overlay ? (
        <img
          src={keyframe}
          alt={`混剪预览${overlay.src}`}
          className="w-full h-full object-cover rounded-sm"
          onError={e => {
            // 图片加载失败时显示默认背景
            e.currentTarget.style.display = 'none'
          }}
        />
      ) : (
        <div className="w-full h-full bg-gray-500 flex items-center justify-center">
          {keyframeLoading ? (
            <div className="text-white/60 text-sm">加载中...</div>
          ) : (
            <div className="text-white/60 text-sm">无预览</div>
          )}
        </div>
      )}
    </>
  )
}

const GeneratedMixcutCard: React.FC<GeneratedMixcut & { index: number }> = ({ index: i, ...combo }) => {
  const { generation, state } = useMixcutContext()

  const firstVideoOverlay = useMemo(() => {
    const { tracks } = state
    // 获取第一个分镜对应的视频轨道索引
    const firstStoryboardTrackIndex = combo.combination[0]

    // 检查轨道索引是否有效
    if (firstStoryboardTrackIndex >= tracks.length) {
      console.warn(`视频轨道索引 ${firstStoryboardTrackIndex} 超出范围，总轨道数: ${tracks.length}`)
      return undefined
    }

    // 获取对应的视频轨道
    const targetVideoTrack = tracks[firstStoryboardTrackIndex]
    if (!targetVideoTrack || !targetVideoTrack.overlays.length) {
      console.warn(`视频轨道 ${firstStoryboardTrackIndex} 不存在或没有视频素材`)
      return undefined
    }

    // 获取轨道中第一个视频 overlay
    const firstVideoOverlay = targetVideoTrack.overlays.find(
      overlay => overlay.storyboardIndex === 0 && overlay.type === OverlayType.VIDEO
    ) as VideoOverlay | null

    if (!firstVideoOverlay || !firstVideoOverlay.src) {
      console.warn(`视频轨道 ${firstStoryboardTrackIndex} 中没有找到有效的视频素材`)
      return undefined
    }

    return firstVideoOverlay
  }, [state.tracks])

  return (
    <MultiSelectableCard {...generation} index={i}>
      <div
        className={clsx(
          'w-48 h-64 relative rounded-sm outline-3 cursor-pointer group',
        )}
      >
        {/* 预览图片背景 */}
        <VideoPreviewFrame overlay={firstVideoOverlay} />

        {/* 重复率标签 */}
        <div className="absolute right-[-8px] top-[-8px] bg-black/70 text-white p-1 text-xs rounded">
          重复率{(combo.similarity * 100).toFixed(1)}%
        </div>

        {/* 播放按钮 - 悬浮时显示在右下角 */}
        <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button
            size="sm"
            variant="secondary"
            className="h-8 w-8 p-0 bg-black/70 hover:bg-black/90 border-0"
            onClick={e => {
              e.stopPropagation()
              generation.setActiveIndex(i)
            }}
          >
            <Play className="w-4 h-4 text-white" />
          </Button>
        </div>
      </div>
    </MultiSelectableCard>

  )
}

// 混剪预览列表
export const GeneratedMixcutListPanel = () => {
  const { generation: { list } } = useMixcutContext()

  if (list.length) {
    return (
      <div className="flex-1 h-fit flex flex-wrap gap-x-4 gap-y-6 p-4 overflow-y-auto">
        {list.map((combo, i) => (
          <GeneratedMixcutCard
            {...combo}
            key={combo.combination.join(',')}
            index={i}
          />
        ))}
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col items-center justify-center p-8 bg-background">
      {/* 空状态插图 */}
      <div className="mb-6">
        <div className="relative">
          {/* 简化的插图 - 人物和列表 */}
          <div className="w-32 h-32 bg-muted rounded-lg flex items-center justify-center">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary/20 rounded-full mx-auto mb-2 flex items-center justify-center">
                <List className="w-8 h-8 text-primary" />
              </div>
              <div className="space-y-1">
                <div className="w-12 h-1 bg-primary/30 rounded mx-auto" />
                <div className="w-8 h-1 bg-primary/20 rounded mx-auto" />
                <div className="w-10 h-1 bg-primary/20 rounded mx-auto" />
              </div>
            </div>
          </div>

          {/* 添加按钮 */}
          <div className="absolute -bottom-2 -right-2">
            <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center shadow-lg">
              <Plus className="w-4 h-4 text-primary-foreground" />
            </div>
          </div>
        </div>
      </div>

      {/* 空状态文本 */}
      <div className="text-center mb-6">
        <p className="text-muted-foreground mb-2">
          未保存视频，请到混剪预览列表中保存视频
        </p>
      </div>

      {/* 添加视频按钮 */}
      <Button className="bg-primary hover:bg-primary/90">
        去生成混剪
      </Button>
    </div>
  )
}

// 混剪规则 Tab 组件
const MixcutRulesTabs = () => {
  const [activeTab, setActiveTab] = useState('material-settings')

  const tabs = [
    { value: 'material-settings', label: '混剪素材设置' },
    { value: 'video-dedup', label: '视频智能去重' },
    { value: 'background-music', label: '背景音乐设置' },
    { value: 'subtitle-style', label: '随机字幕样式设置' },
    { value: 'text-group-style', label: '随机文字组样式设置' },
    { value: 'voice-style', label: '随机口播音色设置' },
    { value: 'background-style', label: '随机背景设置' },
    { value: 'effect-style', label: '随机特效设置' },
  ]

  return (
    <div className="flex h-full">
      {/* 左侧垂直 Tab 导航 */}
      <div className="w-48 border-r border-border bg-muted/30">
        <Tabs
          value={activeTab}
          onValueChange={setActiveTab}
          orientation="vertical"
          className="h-full"
        >
          <TabsList className="flex flex-col h-full w-full justify-start bg-transparent p-2 space-y-1">
            {tabs.map(tab => (
              <TabsTrigger
                key={tab.value}
                value={tab.value}
                className="w-full justify-start text-left px-3 py-2 text-sm font-normal
                  data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm
                  hover:bg-background/50 transition-colors"
              >
                {tab.label}
              </TabsTrigger>
            ))}
          </TabsList>
        </Tabs>
      </div>

      {/* 垂直分割线 */}
      <Separator orientation="vertical" className="h-full" />

      {/* 右侧内容区域 */}
      <div className="flex-1 p-6">
        <Tabs value={activeTab} className="h-full">
          {tabs.map(tab => (
            <TabsContent key={tab.value} value={tab.value} className="h-full">
              <div className="flex items-center justify-center h-full text-muted-foreground">
                {tab.label} 内容区域
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </div>
    </div>
  )
}

// 混剪规则 Drawer 组件
const MixcutRulesDrawer: React.FC<PropsWithChildren> = ({ children }) => {
  return (
    <Drawer  direction="right" >
      <DrawerTrigger asChild>
        {children}
      </DrawerTrigger>

      <DrawerContent className="max-w-[640px] p-0 flex flex-col">
        {/* Drawer 标题栏 */}
        <DrawerHeader className="px-6 py-4 border-b border-border">
          <div className="flex items-center justify-between">
            <DrawerTitle className="text-lg font-medium">
              混剪规则设置
            </DrawerTitle>
            <DrawerClose asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <X className="h-4 w-4" />
              </Button>
            </DrawerClose>
          </div>
        </DrawerHeader>

        {/* Drawer 内容区域 */}
        <div className="flex-1 overflow-hidden">
          <MixcutRulesTabs />
        </div>
      </DrawerContent>
    </Drawer>
  )
}

export const MixcutRuleButton = () => {
  return (
    <MixcutRulesDrawer>
      <Button variant="outline" size="sm">
        <Settings className="w-4 h-4 mr-2" />
        混剪规则
      </Button>
    </MixcutRulesDrawer>
  )
}

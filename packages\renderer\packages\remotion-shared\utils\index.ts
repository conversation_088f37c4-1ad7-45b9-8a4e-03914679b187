/**
 * Get the base URL from environment variables or default to localhost:3000
 */
function getBaseUrl(): string {
  // Use environment variable if available, otherwise default to localhost:3000
  return process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'
}

/**
 * Convert a relative URL to an absolute URL
 *
 * @param url The URL to convert
 * @returns Absolute URL with the correct base
 */
export function toAbsoluteUrl(url: string): string {
  // If the URL is already absolute, return it as is
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }

  // If it's a relative URL starting with /, add the base URL
  if (url.startsWith('/')) {
    return `${getBaseUrl()}${url}`
  }

  // Otherwise, add the base URL with a / separator
  return `${getBaseUrl()}/${url}`
}

import { useState, useMemo } from 'react'
import { useQueryMaterialDirectoryList } from '@/hooks/queries/useQueryMaterial'
import { getPathChain,  } from '@/components/TreeList'

export const useFolderData = (projectId: number) => {
  const [currentFolderId, setCurrentFolderId] = useState<string>('')

  const { data: treeData, isSuccess } = useQueryMaterialDirectoryList({ projectId }, { enabled: !!projectId })

  // 计算路径
  const folderPath = useMemo(() => {
    if (!treeData || !currentFolderId) return []
    return getPathChain(treeData, currentFolderId) ?? [] // 递归计算路径
  }, [treeData, currentFolderId])

  // 获取当前目录下的子目录
  const childFolders = useMemo(() => {
    if (!treeData || !currentFolderId) return []

    const findChildren = (nodes: any[]): any[] => {
      for (const node of nodes) {
        if (String(node.id) === String(currentFolderId)) {
          return node.children || []
        }

        if (node.children && node.children.length > 0) {
          const result = findChildren(node.children)
          if (result.length > 0) {
            return result
          }
        }
      }
      return []
    }

    return findChildren(treeData)
  }, [treeData, currentFolderId])

  return {
    treeData,
    currentFolderId,
    setCurrentFolderId,
    folderPath,
    childFolders,
    isSuccess,
  }
}

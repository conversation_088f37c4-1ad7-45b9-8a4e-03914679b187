/**
 * 统一请求状态同步服务
 * 负责将前端的认证和团队状态同步到Node端
 */

import { IPCRequestState, AuthState, TeamState } from '@app/shared/types/auth-request.types'
import { TokenManager, TeamManager } from '@/libs/storage'

/**
 * 请求状态同步管理器
 */
class IPCRequestStateSync {

  private static instance: IPCRequestStateSync
  private isInitialized = false

  private constructor() {}

  static getInstance(): IPCRequestStateSync {
    if (!IPCRequestStateSync.instance) {
      IPCRequestStateSync.instance = new IPCRequestStateSync()
    }
    return IPCRequestStateSync.instance
  }

  /**
   * 初始化状态同步
   * 在应用启动时调用，同步当前状态到Node端
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return

    try {
      // 获取当前前端状态
      const currentState = this.getCurrentFrontendState()
      
      // 同步到Node端
      await this.syncToNode(currentState)
      
      this.isInitialized = true
    } catch (error) {
      console.error('[IPCRequestStateSync] 初始化失败:', error)
    }
  }

  /**
   * 获取当前前端状态
   */
  private getCurrentFrontendState(): IPCRequestState {
    const authData = TokenManager.getAuthData()
    const auth: AuthState = authData || {
      accessToken: '',
      refreshToken: '',
      expiresTime: 0,
      userId: 0,
      openid: ''
    }

    // 
    const currentTeamId = TeamManager.current()
    const team: TeamState = {
      tenantId: currentTeamId || undefined
    }

    return {
      auth,
      team
    }
  }

  /**
   * 同步认证状态到Node端
   */
  async syncAuthState(auth?: Partial<AuthState>): Promise<void> {
    try {
      const currentState = this.getCurrentFrontendState()
      const authState = auth ? { ...currentState.auth, ...auth } : currentState.auth

      await window.baseInfo.updateRequestState({
        auth: authState
      })

      console.log('[IPCRequestStateSync] 认证状态已同步到Node端:', authState)
    } catch (error) {
      console.error('[IPCRequestStateSync] 同步认证状态失败:', error)
    }
  }

  /**
   * 同步团队状态到Node端
   */
  async syncTeamState(team?: Partial<TeamState>): Promise<void> {
    try {
      const currentState = this.getCurrentFrontendState()
      const teamState = team ? { ...currentState.team, ...team } : currentState.team

      await window.baseInfo.updateRequestState({
        team: teamState
      })

      console.log('[IPCRequestStateSync] 团队状态已同步到Node端:', teamState)
    } catch (error) {
      console.error('[IPCRequestStateSync] 同步团队状态失败:', error)
    }
  }

  /**
   * 同步完整状态到Node端
   */
  async syncToNode(state?: Partial<IPCRequestState>): Promise<void> {
    try {
      const fullState = state || this.getCurrentFrontendState()

      await window.baseInfo.updateRequestState(fullState)

      console.log('[IPCRequestStateSync] 完整状态已同步到Node端:', fullState)
    } catch (error) {
      console.error('[IPCRequestStateSync] 同步完整状态失败:', error)
    }
  }

  /**
   * 清除Node端状态
   */
  async clearNodeState(): Promise<void> {
    try {
      await window.baseInfo.clearRequestState()
      console.log('[IPCRequestStateSync] Node端状态已清除')
    } catch (error) {
      console.error('[IPCRequestStateSync] 清除Node端状态失败:', error)
    }
  }

  /**
   * 检查Node端token是否过期
   */
  async isNodeTokenExpired(): Promise<boolean> {
    try {
      return await window.baseInfo.isTokenExpired()
    } catch (error) {
      console.error('[IPCRequestStateSync] 检查Node端token过期状态失败:', error)
      return false
    }
  }

  /**
   * 获取Node端当前状态
   */
  async getNodeState(): Promise<IPCRequestState | null> {
    try {
      return await window.baseInfo.getRequestState()
    } catch (error) {
      console.error('[IPCRequestStateSync] 获取Node端状态失败:', error)
      return null
    }
  }

  // 便捷方法：监听前端状态变化并自动同步
  
  /**
   * 当用户登录时调用
   */
  async onUserLogin(): Promise<void> {
    await this.syncAuthState()
  }

  /**
   * 当用户登出时调用
   */
  async onUserLogout(): Promise<void> {
    await this.clearNodeState()
  }

  /**
   * 当切换团队时调用
   */
  async onTeamSwitch(teamId?: number): Promise<void> {
    await this.syncTeamState({ tenantId: teamId })
  }

  /**
   * 当token刷新时调用
   */
  async onTokenRefresh(): Promise<void> {
    await this.syncAuthState()
  }
}

// 导出单例实例
export const ipcRequestStateSync = IPCRequestStateSync.getInstance()

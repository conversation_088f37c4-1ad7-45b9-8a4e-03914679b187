import { Injectable, Inject } from '@nestjs/common'
import { Folder } from '@app/shared/types/database.types.js'
import { BaseRepository } from '@/infra/types/BaseRepository.js'
import { FolderModel } from '@/infra/models/FolderModel.js'
import { NestDatabaseService } from '@/modules/global/database.service.js'

/**
 * 文件夹仓储类
 */
@Injectable()
export class FolderRepository extends BaseRepository<FolderModel, Folder.CreateParams, Folder.UpdateParams, Folder.QueryParams> {

  /**
   * 表名
   */
  protected readonly tableName = 'folder'

  /**
   * 主键字段名
   */
  protected readonly primaryKey = 'id'

  /**
   * 可排序字段列表
   */
  protected readonly sortableFields = [
    'id', 'parent_id', 'material_type', 'title',
    'updated_at', 'created_at',
  ]

  /**
   * 可查询字段列表
   */
  protected readonly queryableFields = [
    'id', 'parent_id', 'deleted_at', 'material_type',
    'team_id', 'uid', 'auto_create',
  ]

  /**
   * 构造函数
   * @param dbService 数据库服务实例
   */
  constructor(
    @Inject(NestDatabaseService)
    dbService: NestDatabaseService
  ) {
    super(dbService)
  }

  /**
   * 将数据库记录转换为领域模型
   * @param data 数据库记录
   * @returns 文件夹领域模型
   */
  toModel(data: Record<string, any> | null): FolderModel | null {
    if (!data) return null
    return new FolderModel(data)
  }

  /**
   * 查找子文件夹
   * @param parentId 父文件夹ID
   * @param uid 用户ID
   * @param teamId 团队ID
   * @returns 子文件夹列表
   */
  findChildren(parentId: number, uid: string, teamId?: number | null): FolderModel[] {
    let sql = `
      SELECT * FROM ${this.tableName}
      WHERE parent_id = ? AND uid = ? AND deleted_at = 0
    `

    const params: any[] = [parentId, uid]

    if (teamId !== undefined) {
      sql += ' AND team_id = ?'
      params.push(teamId)
    }

    sql += ' ORDER BY created_at ASC'

    const stmt = this.db.prepare(sql)
    const data = stmt.all(...params) as Record<string, any>[]
    return this.toModelArray(data)
  }

  /**
   * 获取文件夹路径
   * @param id 文件夹ID
   * @returns 文件夹路径数组
   */
  getFolderPath(id: number): FolderModel[] {
    const path: FolderModel[] = []
    let currentId = id

    while (currentId && currentId !== 0) {
      const stmt = this.db.prepare(`SELECT * FROM ${this.tableName} WHERE id = ? AND deleted_at = 0`)
      const data = stmt.get(currentId) as Record<string, any> | undefined

      if (!data) {
        break
      }

      const folder = this.toModel(data)
      if (folder) {
        path.unshift(folder)
        currentId = folder.parent_id
      }
    }

    return path
  }
}

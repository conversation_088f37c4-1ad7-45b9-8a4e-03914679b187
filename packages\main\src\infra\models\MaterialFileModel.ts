import { MaterialFile } from '@app/shared/types/database.types.js'

/**
 * 素材文件领域模型类
 * 代表素材文件实体及其业务规则
 */
export class MaterialFileModel implements MaterialFile.IMaterialFile {

  /**
     * 素材文件ID
     */
  id: string

  /**
     * 所属文件夹ID
     */
  parent_id: number

  /**
     * 团队ID
     */
  team_id: number | null

  /**
     * 用户ID
     */
  uid: string

  /**
     * 素材类型
     * 1: 视频, 2: 音频, 3: 图片, 等
     */
  material_type: number

  /**
     * 文件路径
     */
  path: string

  /**
     * 原始文件路径
     */
  source_path: string

  /**
     * 文件大小（字节）
     */
  size: number

  /**
     * 媒体时长（毫秒）
     */
  duration: number

  /**
     * 像素宽度
     */
  width: number

  /**
     * 像素高度
     */
  height: number

  /**
     * 编码器名称
     */
  codec_name: string

  /**
     * 编码类型（audio/video）
     */
  codec_type: string

  /**
     * 码率
     */
  bit_rate: number | null

  /**
     * 总帧数
     */
  nb_frames: number | null

  /**
     * 封面图路径
     */
  cover: string

  /**
     * 文件哈希
     */
  hash: string

  /**
     * 状态
     * 0: 未处理, 1: 处理中, 2: 处理完成, 3: 处理失败
     */
  status: number

  /**
     * 存储位置
     * 0: 初始, 1: 云端, 2: 本地
     */
  clip_cloud_or_local: number

  /**
     * 对象ID
     */
  object_oid: string

  /**
     * 失败原因
     */
  reason: string

  /**
     * 文件名
     */
  title: string

  /**
     * 上传ID
     */
  upload_id: number

  /**
     * 关联任务编号
     */
  task_no: string | null

  /**
     * 裁剪信息(JSON)
     */
  clipinfo: string

  /**
     * 更新时间戳
     */
  updated_at: number

  /**
     * 删除时间戳（0表示未删除）
     */
  deleted_at: number

  /**
     * 创建时间戳
     */
  created_at: number

  /**
     * 标签ID
     */
  tag_id: number

  /**
     * 构造函数
     * @param data 素材文件数据
     */
  constructor(data: Partial<MaterialFileModel> = {}) {
    this.id = data.id ?? ''
    this.parent_id = data.parent_id ?? 0
    this.team_id = data.team_id ?? null
    this.uid = data.uid ?? ''
    this.material_type = data.material_type ?? 1
    this.path = data.path ?? ''
    this.source_path = data.source_path ?? ''
    this.size = data.size ?? 0
    this.duration = data.duration ?? 0
    this.width = data.width ?? 0
    this.height = data.height ?? 0
    this.codec_name = data.codec_name ?? ''
    this.codec_type = data.codec_type ?? ''
    this.bit_rate = data.bit_rate ?? null
    this.nb_frames = data.nb_frames ?? null
    this.cover = data.cover ?? ''
    this.hash = data.hash ?? ''
    this.status = data.status ?? 0
    this.clip_cloud_or_local = data.clip_cloud_or_local ?? 0
    this.object_oid = data.object_oid ?? ''
    this.reason = data.reason ?? ''
    this.title = data.title ?? ''
    this.upload_id = data.upload_id ?? 0
    this.task_no = data.task_no ?? null
    this.clipinfo = data.clipinfo ?? ''
    this.updated_at = data.updated_at ?? Date.now()
    this.deleted_at = data.deleted_at ?? 0
    this.created_at = data.created_at ?? Date.now()
    this.tag_id = data.tag_id ?? 0
  }

  /**
     * 判断是否已删除
     */
  isDeleted(): boolean {
    return this.deleted_at > 0
  }

  /**
     * 判断是否属于指定用户
     * @param uid 用户ID
     */
  belongsToUser(uid: string): boolean {
    return this.uid === uid
  }

  /**
     * 判断是否属于指定团队
     * @param teamId 团队ID
     */
  belongsToTeam(teamId: number): boolean {
    return this.team_id === teamId
  }

  /**
     * 判断是否为视频素材
     */
  isVideo(): boolean {
    return this.material_type === 1
  }

  /**
     * 判断是否为音频素材
     */
  isAudio(): boolean {
    return this.material_type === 2
  }

  /**
     * 判断是否为图片素材
     */
  isImage(): boolean {
    return this.material_type === 3
  }

  /**
     * 获取素材文件的显示名称
     */
  getDisplayName(): string {
    return this.title || '未命名素材'
  }

  /**
     * 获取素材文件的时长（格式化）
     * @returns 格式化的时长，如 "01:23:45"
     */
  getFormattedDuration(): string {
    const totalSeconds = Math.floor(this.duration / 1000)
    const hours = Math.floor(totalSeconds / 3600)
    const minutes = Math.floor((totalSeconds % 3600) / 60)
    const seconds = totalSeconds % 60

    return [
      hours.toString().padStart(2, '0'),
      minutes.toString().padStart(2, '0'),
      seconds.toString().padStart(2, '0'),
    ].join(':')
  }

  /**
     * 获取素材文件的大小（格式化）
     * @returns 格式化的文件大小，如 "1.23 MB"
     */
  getFormattedSize(): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB']
    let size = this.size
    let unitIndex = 0

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }

    return `${size.toFixed(2)} ${units[unitIndex]}`
  }

  /**
     * 获取素材文件的分辨率
     * @returns 分辨率，如 "1920x1080"
     */
  getResolution(): string {
    if (this.width === 0 || this.height === 0) {
      return '未知'
    }
    return `${this.width}x${this.height}`
  }

  /**
     * 将素材文件转换为JSON对象
     */
  toJSON(): Record<string, any> {
    return {
      id: this.id,
      parent_id: this.parent_id,
      team_id: this.team_id,
      uid: this.uid,
      material_type: this.material_type,
      path: this.path,
      source_path: this.source_path,
      size: this.size,
      duration: this.duration,
      width: this.width,
      height: this.height,
      codec_name: this.codec_name,
      codec_type: this.codec_type,
      bit_rate: this.bit_rate,
      nb_frames: this.nb_frames,
      cover: this.cover,
      hash: this.hash,
      status: this.status,
      clip_cloud_or_local: this.clip_cloud_or_local,
      object_oid: this.object_oid,
      reason: this.reason,
      title: this.title,
      upload_id: this.upload_id,
      task_no: this.task_no,
      clipinfo: this.clipinfo,
      updated_at: this.updated_at,
      deleted_at: this.deleted_at,
      created_at: this.created_at,
      tag_id: this.tag_id,
    }
  }
}

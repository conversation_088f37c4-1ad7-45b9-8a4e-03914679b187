{"name": "clipnest-client", "version": "0.0.1", "private": true, "type": "module", "main": "packages/entry-point.mjs", "scripts": {"start": "chcp 65001 && node packages/dev-mode.js ", "package": "npm run build && electron-builder build --config electron-builder.mjs", "build": "npm run build -ws --if-present", "test": "npx playwright test ./tests/e2e.spec.ts", "typecheck": "npm run typecheck -ws --if-present", "create-renderer": "cd packages && npm create vite@latest renderer", "integrate-renderer": "npm start --workspace @app/integrate-renderer", "init": "npm run create-renderer && npm run integrate-renderer && npm install", "lint": "npm run lint -ws --if-present", "lint:fix": "npm run lint:fix -ws --if-present", "postinstall": "npx electron-rebuild"}, "workspaces": ["packages/*"], "engines": {"node": ">=23.0.0"}, "dependencies": {"@app/main": "*", "@types/ali-oss": "^6.16.11", "@types/better-sqlite3": "^7.6.13", "ali-oss": "^6.23.0", "better-sqlite3": "^11.10.0", "dunder-proto": "^1.0.1", "jszip": "^3.10.1", "react-day-picker": "^9.8.0", "react-router-dom": "^7.6.3", "react-simple-code-editor": "^0.14.1", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@npmcli/map-workspaces": "4.0.2", "@playwright/test": "1.52.0", "@stylistic/eslint-plugin": "^5.1.0", "@types/node": "22.15.29", "@types/uuid": "^10.0.0", "electron": "36.3.2", "electron-builder": "26.0.12", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-unused-imports": "^4.1.4", "glob": "11.0.2", "playwright": "^1.52.0"}}
import { ScriptSceneData } from '@/hooks/queries/useQueryScript'
import { round } from 'lodash'

export function extractSentencesFromScriptScene(scene: ScriptSceneData) {
  if (!scene.script) return []

  return scene.script
    .split('\n')
    .map(line => line.trim())
    .filter(line => line.length > 0)
}

export function estimateSentencesDuration(sentences: string[]): [number | undefined, number | undefined] {
  if (!sentences?.length) return [undefined, undefined]

  // 简单预估10个字符为 1 秒
  const sentenceDuration = (sentence: string) => sentence.length / 10

  const durations = sentences.map(sentenceDuration)

  const min = round(Math.min(...durations), 2)
  const max = round(Math.max(...durations), 2)

  if (min !== max) {
    return [min, max]
  }

  return [min, undefined]
}

export function md5(buffer: ArrayBuffer): string {
  function toHex(num: number) {
    return ('0' + num.toString(16)).slice(-2)
  }

  const data = new Uint8Array(buffer)
  const k: number[] = []
  for (let i = 0; i < 64;) k[i] = (Math.sin(++i) * 4294967296) | 0

  const h = [1732584193, -271733879, ~1732584193, ~-271733879]
  const blocks = new Uint8Array(64)
  const words = new Uint32Array(16)

  for (let i = 0; i <= data.length; i += 64) {
    blocks.fill(0)
    blocks.set(data.subarray(i, i + 64))

    if (i + 64 > data.length) {
      blocks[data.length % 64] = 0x80
      if (data.length % 64 >= 56) {
        processBlock(h, blocks, words, k)
        blocks.fill(0)
      }
      const bitLen = data.length * 8
      new DataView(blocks.buffer).setUint32(56, bitLen, true)
    }
    processBlock(h, blocks, words, k)
  }

  return h
    .map(n =>
      toHex(n & 0xff) +
      toHex((n >>> 8) & 0xff) +
      toHex((n >>> 16) & 0xff) +
      toHex((n >>> 24) & 0xff)
    )
    .join('')
}

function processBlock(h: number[], blocks: Uint8Array, words: Uint32Array, k: number[]) {
  for (let j = 0; j < 16; j++) {
    words[j] =
      blocks[j * 4] |
      (blocks[j * 4 + 1] << 8) |
      (blocks[j * 4 + 2] << 16) |
      (blocks[j * 4 + 3] << 24)
  }

  let [a, b, c, d] = h
  const shifts = [7, 12, 17, 22, 5, 9, 14, 20, 4, 11, 16, 23, 6, 10, 15, 21]

  for (let j = 0; j < 64; j++) {
    const div16 = j >> 4
    const f =
      div16 === 0
        ? (b & c) | (~b & d)
        : div16 === 1
          ? (d & b) | (~d & c)
          : div16 === 2
            ? b ^ c ^ d
            : c ^ (b | ~d)
    const temp = (a + f + k[j] + words[j % 16]) | 0
    a = d
    d = c
    c = b
    b = (b + ((temp << shifts[(div16 << 2) | (j & 3)]) | (temp >>> (32 - shifts[(div16 << 2) | (j & 3)])))) | 0
  }

  h[0] = (h[0] + a) | 0
  h[1] = (h[1] + b) | 0
  h[2] = (h[2] + c) | 0
  h[3] = (h[3] + d) | 0
}

import { useQuery } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { ResourceModule } from '@/libs/request/api/resource'

function toReversed<T>(arr: T[]) {
  const iter = (ls: T[], sum: T[]) => {
    if (ls.length === 0) return sum
    const [first, ...rest] = ls
    return iter(rest, [first, ...sum])
  }
  return iter(arr, [])
}

export const useQuerySystemDict = (type: string, reverse = true) => {
  return useQuery({
    queryKey: [QUERY_KEYS.SYSTEM_DICT, type, reverse],
    queryFn: async () => {
      const data = await ResourceModule.systemDict.getType({ type })
      if (!reverse) return data
      return toReversed(data)
    },
  })
}

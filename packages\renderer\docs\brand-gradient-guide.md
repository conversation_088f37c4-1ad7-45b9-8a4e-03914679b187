# 品牌渐变主题色使用指南

## 概述
项目中定义了一套完整的品牌渐变主题色系统，基于以下专业色彩定义：
```css
--color-primary-highlight: #7EE4D2;  /* 高亮色 */
--color-primary-base: #D7CAB3;       /* 基础色 */
--color-primary-accent: #F77E54;     /* 强调色 */

--gradient-brand: linear-gradient(
  to right,
  var(--color-primary-highlight),
  var(--color-primary-base),
  var(--color-primary-accent)
);
```

## 可用的CSS变量

### 品牌渐变
- `--gradient-brand`: 水平渐变 (从左到右)
- `--gradient-brand-vertical`: 垂直渐变 (从上到下)
- `--gradient-brand-diagonal`: 对角渐变 (135度)
- `--gradient-brand-radial`: 径向渐变

### Primary主题色
- `--color-primary-highlight`: #7EE4D2 (高亮色)
- `--color-primary-base`: #D7CAB3 (基础色)
- `--color-primary-accent`: #F77E54 (强调色)

## Tailwind CSS 实用类

### 背景渐变
```tsx
// 水平渐变背景
<div className="bg-gradient-brand">内容</div>

// 垂直渐变背景
<div className="bg-gradient-brand-vertical">内容</div>

// 对角渐变背景
<div className="bg-gradient-brand-diagonal">内容</div>

// 径向渐变背景
<div className="bg-gradient-brand-radial">内容</div>
```

### 渐变文字
```tsx
// 渐变文字效果
<h1 className="text-gradient-brand">品牌渐变标题</h1>
```

### 渐变边框
```tsx
// 亮色模式渐变边框
<div className="border-gradient-brand">内容</div>

// 暗色模式渐变边框
<div className="border-gradient-brand-dark">内容</div>
```

### 单色使用
```tsx
// 使用品牌主题色
<div className="bg-[var(--color-primary-highlight)]">高亮色背景</div>
<div className="bg-[var(--color-primary-base)]">基础色背景</div>
<div className="bg-[var(--color-primary-accent)]">强调色背景</div>

// 文字颜色
<span className="text-[var(--color-primary-highlight)]">高亮色文字</span>
<span className="text-[var(--color-primary-base)]">基础色文字</span>
<span className="text-[var(--color-primary-accent)]">强调色文字</span>
```

## 使用示例

### 按钮组件
```tsx
// 品牌渐变背景按钮
<button className="px-4 py-2 bg-gradient-brand text-white rounded-lg hover:opacity-90 transition-opacity">
  品牌按钮
</button>

// 渐变边框按钮
<button className="px-4 py-2 border-gradient-brand bg-transparent rounded-lg">
  渐变边框按钮
</button>

// 单色主题按钮
<button className="px-4 py-2 bg-[var(--color-primary-accent)] text-white rounded-lg">
  强调色按钮
</button>
```

### 卡片组件
```tsx
// 品牌渐变背景卡片
<div className="bg-gradient-brand-diagonal p-6 rounded-lg text-white">
  <h3>品牌渐变卡片</h3>
  <p>内容...</p>
</div>

// 渐变边框卡片
<div className="border-gradient-brand bg-white p-6 rounded-lg">
  <h3>渐变边框卡片</h3>
  <p>内容...</p>
</div>
```

### 标题组件
```tsx
// 品牌渐变文字标题
<h1 className="text-4xl font-bold text-gradient-brand">
  品牌主标题
</h1>

// 混合使用
<div className="bg-gradient-brand-radial p-8 rounded-xl">
  <h2 className="text-white text-2xl font-semibold mb-4">
    白色标题
  </h2>
  <p className="text-white/80">
    描述文字
  </p>
</div>
```

## 暗色模式支持
所有渐变在暗色模式下会自动调整为更适合的色调：
- 亮色模式: `#7EE4D2 → #D7CAB3 → #F77E54`
- 暗色模式: `#5accc0 → #b8ac98 → #f77e54`

## 色彩语义
- **Highlight (#7EE4D2)**: 用于高亮、悬停状态、重要信息提示
- **Base (#D7CAB3)**: 用于主要内容、默认状态、中性元素
- **Accent (#F77E54)**: 用于强调、行动召唤、警告提示

## 最佳实践
1. 优先使用预定义的实用类而不是直接使用CSS变量
2. 在暗色模式下测试渐变效果
3. 注意渐变文字的可读性，必要时添加背景或阴影
4. 渐变边框在某些背景下可能不明显，考虑添加阴影或调整透明度
5. 根据色彩语义选择合适的单色使用场景

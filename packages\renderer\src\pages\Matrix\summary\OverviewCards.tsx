import React, { useState, useMemo, PropsWithChildren } from 'react'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Calendar } from 'lucide-react'
import { useQueryDyAccountOverview } from '@/hooks/queries/useQueryMatrix'
import dayjs from 'dayjs'
import { formatTimestamp } from '@/components/lib/utils'

// 时间范围选项
const TIME_RANGE_OPTIONS = [
  { value: 'yesterday', label: '昨天' },
  { value: 'last7days', label: '近7天' },
] as const

type TimeRangeValue = typeof TIME_RANGE_OPTIONS[number]['value']

interface DataItemCardProps extends PropsWithChildren {
  icon?: any
  color?: string
  bgColor?: string
  className?: string
}

const DataItemCard = ({
  icon,
  color,
  bgColor,
  className = '',
  children
}: DataItemCardProps) => {
  const Icon = icon

  return (
    <div className={`px-4 py-3 rounded-lg border hover:shadow-md transition-shadow bg-background/50 ${className}`}>
      {children}

      <div className={`p-2 rounded-full ${bgColor}`}>
        {icon && <Icon className={`h-4 w-4 ${color}`} />}
      </div>
    </div>
  )
}

const DataValueItem = ({
  title,
  value,

}: { title: string, value?: string | number }) => {
  return (
    <div className="flex flex-col gap-3">
      <div className="text-sm font-medium text-muted">
        {title}
      </div>

      <div className="text-3xl font-bold">
        {value}
      </div>
    </div>
  )
}

export const OverviewCards = () => {
  const [selectedTimeRange, setSelectedTimeRange] = useState<TimeRangeValue>('last7days')

  const timeParams = useMemo(() => {
    if (selectedTimeRange === 'yesterday') {
      const yesterday = dayjs().subtract(1, 'day')
      return {
        startTime: yesterday.startOf('day').valueOf(),
        endTime: yesterday.endOf('day').valueOf()
      }
    } else {
      return {
        startTime: dayjs().subtract(7, 'day').startOf('day').valueOf(),
        endTime: dayjs().endOf('day').valueOf()
      }
    }
  }, [selectedTimeRange])

  const { data: overview } = useQueryDyAccountOverview(timeParams)

  // 处理时间范围变化
  const handleTimeRangeChange = (value: string) => {
    if (value === 'yesterday' || value === 'last7days') {
      setSelectedTimeRange(value)
    }
  }

  // const formatNumber = (num: number) => {
  //   if (num >= 10000) {
  //     return (num / 10000).toFixed(1) + 'w'
  //   }
  //   return num.toString()
  // }

  return (
    <div className="space-y-4">
      {/* 时间选择器 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h2 className="text-lg font-semibold text-gradient-brand">视频数据概览</h2>
          <span className="text-sm text-muted">({formatTimestamp(timeParams.startTime)} ~ {formatTimestamp(timeParams.endTime)})</span>
        </div>

        <div className="flex items-center gap-2">
          <Calendar className="h-4 w-4 text-gray-500" />
          <Select value={selectedTimeRange} onValueChange={handleTimeRangeChange}>
            <SelectTrigger className="w-[120px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {TIME_RANGE_OPTIONS.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* 数据卡片 */}
      <div className="space-y-4">
        {/* 第一行：主要数据卡片 */}
        <div className="grid grid-cols-3 gap-4">
          <DataItemCard>
            <DataValueItem
              title="授权账号数"
              value={overview?.authAccount || 0}
            />
          </DataItemCard>
          <DataItemCard>
            <DataValueItem
              title="发布视频数"
              value={ overview?.totalVideo || 0}
            />
          </DataItemCard>

          <DataItemCard>
            <div className="flex itemcenter justify-start gap-8">
              <DataValueItem
                title="截止昨日总粉丝数"
                value={ overview?.fans || 0}
              />
              <DataValueItem
                title="近7天净增粉丝数"
                value={ overview?.changeFans || 0}
              />
            </div>
          </DataItemCard>

        </div>

        {/* 第二行：次要数据卡片 */}

        <div className="grid grid-cols-4 gap-4">
          <DataItemCard>
            <DataValueItem
              title="播放量"
              value={overview?.playNum || 0}
            />
          </DataItemCard>
          <DataItemCard>
            <DataValueItem
              title="点赞数"
              value={overview?.diggNum || 0}
            />
          </DataItemCard>
          <DataItemCard>
            <DataValueItem
              title="评论量"
              value={overview?.commentNum || 0}
            />
          </DataItemCard>
          <DataItemCard>
            <DataValueItem
              title="分享量"
              value={overview?.shareNum || 0}
            />
          </DataItemCard>
        </div>
      </div>
    </div>
  )
}


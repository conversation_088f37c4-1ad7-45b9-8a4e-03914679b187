import { useQuery } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'
import { ResourceModule } from '@/libs/request/api/resource'
import { useInfiniteQuery } from '../useInfiniteQuery'
import { BaseResourceQueryParams, PasterResource } from '@/types/resources'
import { buildTreeFromFlatList } from '@/components/TreeList'

import { CommonDefaultCategoryEnum } from '@/modules/video-editor/shared'

export const useQueryPasterList = (params: BaseResourceQueryParams) => {
  return useQuery({
    queryKey: [QUERY_KEYS.PASTER_LIST, params],
    queryFn: () => ResourceModule.paster.list(params),
  })
}

export const useQueryPasterCategory = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.PASTER_CATEGORY],
    queryFn: () => ResourceModule.paster.category(),
  })
}

export const useQueryPasterDirList = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.LOCAL_PASTER_FOLDER_LIST],
    queryFn: async () => {
      const flatList = await ResourceModule.paster.dirList()
      return buildTreeFromFlatList(flatList)
    },
  })
}

/**
 * 使用无限查询获取贴纸本地资源列表，支持无限滚动加载
 * @param params 查询参数，包括文件夹ID和每页大小
 * @returns 无限查询结果
 */
export const useInfiniteQueryLocalPasterList = (
  params: BaseResourceQueryParams & { folderUuid: string },
) => {
  return useInfiniteQuery<PasterResource.Paster>(
    [QUERY_KEYS.LOCAL_PASTER_LIST],
    ResourceModule.paster.localList,
    params,
    {
      pageSize: params.pageSize || 12,
      enabled: params.folderUuid !== '',
    },
  )
}

/**
 * 使用无限查询获取贴纸列表，支持无限滚动加载
 * @param params 查询参数，包括分类ID和每页大小
 * @returns 无限查询结果
 */
export const useInfiniteQueryPasterList = (params: BaseResourceQueryParams) => {
  return useInfiniteQuery<PasterResource.Paster>(
    [QUERY_KEYS.PASTER_LIST],
    ResourceModule.paster.list,
    params,
    {
      pageSize: params.pageSize || 12,
      enabled: params.categoryIds ? params.categoryIds.length > 0 : true,
    },
  )
}

/**
 * 使用无限查询获取收藏的贴纸列表，支持无限滚动加载
 * @param params 查询参数
 * @returns 无限查询结果
 */
export const useInfiniteQueryPasterCollectedList = (params: BaseResourceQueryParams) => {
  return useInfiniteQuery<PasterResource.Paster>(
    [QUERY_KEYS.PASTER_COLLECTED],
    ResourceModule.paster.collected,
    params,
    {
      pageSize: params.pageSize || 12,
    },
  )
}

/**
 * 统一的贴纸查询hook，根据选择的分类自动决定使用哪个接口
 * @param params 查询参数，包括选中的分类ID
 * @returns 无限查询结果
 */
export const useInfiniteQueryPasterUnified = (
  params: BaseResourceQueryParams & { selectedCategory?: string },
) => {
  const { selectedCategory, ...queryParams } = params

  const isCollectedCategory = selectedCategory === CommonDefaultCategoryEnum.COLLECTED

  const finalParams = isCollectedCategory
    ? queryParams // 收藏列表不需要 categoryIds
    : { ...queryParams,
      categoryIds:
          !selectedCategory || selectedCategory === CommonDefaultCategoryEnum.ALL
            ? undefined
            : [selectedCategory], }

  if (isCollectedCategory) {
    return useInfiniteQuery<PasterResource.Paster>(
      [QUERY_KEYS.PASTER_COLLECTED, finalParams],
      ResourceModule.paster.collected,
      finalParams,
      {
        pageSize: finalParams.pageSize || 12,
      },
    )
  } else {
    return useInfiniteQuery<PasterResource.Paster>(
      [QUERY_KEYS.PASTER_LIST, finalParams],
      ResourceModule.paster.list,
      finalParams,
      {
        pageSize: finalParams.pageSize || 12,
        enabled: finalParams.categoryIds ? finalParams.categoryIds.length > 0 : true,
      },
    )
  }
}

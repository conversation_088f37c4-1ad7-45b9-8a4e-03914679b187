import { QUERY_KEYS } from '@/constants/queryKeys'
import { TeamAPI } from '@/libs/request/api/team'
import { useQuery } from '@tanstack/react-query'

export const useQueryCurrentTeam = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.TEAM_CURRENT],
    queryFn: () => TeamAPI.current({}),
  })
}

export const useQueryTeamMemberList = (keyword?: string) => {
  return useQuery({
    queryKey: [QUERY_KEYS.TEAM_MEMBER_LIST, keyword],
    queryFn: () => TeamAPI.members({ keyword }),
  })
}

export const useQueryTeamMemberByID = (memberId?: number | null) => {
  return useQuery({
    queryKey: [QUERY_KEYS.TEAM_MEMBER, memberId],
    queryFn: () => TeamAPI.member({ memberId: memberId! }),
    enabled: !!memberId,
  })
}

export const useQueryTeamRoles = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.TEAM_ROLES],
    queryFn: () => TeamAPI.roles({}),
  })
}

export const useQueryTeamList = () => {
  return useQuery({
    queryKey: [QUERY_KEYS.TEAM_LIST],
    queryFn: () => TeamAPI.list({}),
  })
}

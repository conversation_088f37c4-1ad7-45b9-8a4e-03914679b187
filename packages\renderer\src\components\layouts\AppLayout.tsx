import React, { FC, Suspense } from 'react'
import { KeepAlive, TabManager } from '@/components/TabSystem'
import useVirtualTabsStore, { VirtualizableComponents } from '@/libs/stores/useVirtualTabsStore'
import { Outlet } from 'react-router'
import { PageLoading } from '../LoadingIndicator'
import { VirtualTabContext } from '@/contexts'

const TitleBar: FC = () => {
  const handleMinimize = () => window.windowManager.minimize()
  const handleMaximize = () => window.windowManager.maximize()
  const handleClose = () => window.windowManager.close()

  return (
    <div className="h-[40px] flex items-center justify-between bg-neutral-100 dark:bg-neutral-800 border-b border-neutral-200 dark:border-neutral-700 app-drag-region">
      <div className="flex items-center h-full">
        <div className="px-3 select-none h-full">
          <img src="/text-logo.png" alt="ClipNest" className="aspect-auto h-full" />
        </div>
      </div>
      <div className="flex-1">
        <TabManager />
      </div>
      <div className="flex items-center app-no-drag">
        {/* 窗口控制按钮 */}
        <button className="p-2 hover:bg-neutral-200 dark:hover:bg-neutral-700" onClick={handleMinimize} title="最小化">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <line x1="5" y1="12" x2="19" y2="12" />
          </svg>
        </button>
        <button className="p-2 hover:bg-neutral-200 dark:hover:bg-neutral-700" onClick={handleMaximize} title="最大化">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
          </svg>
        </button>
        <button
          className="p-2 hover:bg-red-500 text-neutral-700 hover:text-white dark:text-neutral-200"
          onClick={handleClose}
          title="关闭"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <line x1="18" y1="6" x2="6" y2="18" />
            <line x1="6" y1="6" x2="18" y2="18" />
          </svg>
        </button>
      </div>
    </div>
  )
}

const MainContentArea: FC = () => {
  const { tabs, activeTabId } = useVirtualTabsStore()

  return (
    <div className="flex-1 overflow-hidden" id="MainContentArea">
      <KeepAlive active={activeTabId === null} name="home">
        <Outlet />
      </KeepAlive>

      <Suspense fallback={<PageLoading />}>
        <div className="h-full overflow-hidden relative">
          {!!tabs.length &&
            tabs.map(tab => (
              <KeepAlive key={tab.id} name={tab.id} active={activeTabId === tab.id}>
                <VirtualTabContext.Provider value={tab}>
                  {React.createElement(VirtualizableComponents[tab.componentKey])}
                </VirtualTabContext.Provider>
              </KeepAlive>
            ))}
        </div>
      </Suspense>
    </div>
  )
}

const Footer: FC = () => {
  return (
    <footer className="h-6 flex items-center justify-between px-4 bg-neutral-100 dark:bg-neutral-800 border-t border-neutral-200 dark:border-neutral-700 text-xs text-gray-500">
      <span />
      <span>闪剪罐头 {import.meta.env.VITE_APP_VERSION}</span>
    </footer>
  )
}

export const AppLayout = () => {
  return (
    <div className="h-screen w-screen flex flex-col bg-white dark:bg-neutral-900 text-neutral-900 dark:text-white">
      <TitleBar />
      <MainContentArea />
      <Footer />
    </div>
  )
}

import request, { fetchPagination } from '../request'
import { PaginationParams } from '@app/shared/types/database.types'
import {
  Account, AccountPushDetail, AccountPushDetailRequestParams,
  AccountSearchParams,
  CreateGroup, CreateMatrixParams, DyAccountOverview, DyPublishFormDetail, DyPushPlan,
  GroupAccount,
  GroupAccountSearchParams,
  GroupItem, TimeRangeParams,
  UpdateGroup
} from '@/types/matrix/douyin'

export const MatrixModule = {

  createGroup(data: CreateGroup) {
    return request.post('/app-api/publishing/dy-group/add', data)
  },
  updateGroup(data: UpdateGroup) {
    return request.put('/app-api/publishing/dy-group/update', data)
  },
  deleteGroup(id: number) {
    return request.delete(`/app-api/publishing/dy-group/delete?id=${id}`)
  },
  groupList() {
    return request.get<GroupItem[]>('/app-api/publishing/dy-group/list')
  },
  groupAccount(data: GroupAccountSearchParams) {
    return fetchPagination<GroupAccount>('/app-api/publishing/dy-group/account-page', data)
  },
  addAccountToGroup(data: { groupId: number, accountIds: number[] }) {
    return request.post('/app-api/publishing/dy-group/add-account', data)
  },
  deleteAccountFromGroup(data: { groupId: number, accountIds: number[] }) {
    return request.post('/app-api/publishing/dy-group/delete-account', data)
  },
  dyAccount: {
    list: (params: AccountSearchParams) => {
      return fetchPagination<Account>('/app-api/publishing/dy-account/page', params)
    },
    accountPushDetail(data: AccountPushDetailRequestParams) {
      return fetchPagination<AccountPushDetail>('/app-api/publishing/dy-push-detail/page', data)
    },
    accountPlanList(data: { accountIds: number[], limit: number }) {
      return request.post<{ id: number, name: string }>('/app-api/publishing/dy-push-plan/list-plan', data)
    },
    getCode() {
      return request.get<string>('/app-api/publishing/dy-auth/get-code')
    },
    overview(data?: TimeRangeParams) {
      return request.post<DyAccountOverview>('/app-api/publishing/dy-account/overview', data)
    },
    pushPlanList(data: PaginationParams) {
      return fetchPagination<DyPushPlan>('/app-api/publishing/dy-push-plan/page', data)
    },
    saveDraft(data: CreateMatrixParams & { id?: number }) {
      return request.post('/app-api/publishing/dy-push-draft/save', data)
    },
    draftList() {
      return request.get<(CreateMatrixParams & { id: number })[]>('/app-api/publishing/dy-push-draft/list')
    },
    publish(data: CreateMatrixParams) {
      return request.post('/app-api/publishing/dy-push-plan/create', data)
    },
    deleteDraft(id: number) {
      return request.delete(`/app-api/publishing/dy-push-draft/delete?id=${id}`)
    },
    batchDeleteDraft(params: { ids: number[] }) {
      return request.delete(`/app-api/publishing/dy-push-draft/delete-list?ids=${params.ids}`)
    },
    taskDetail(id: number) {
      return request.get<DyPublishFormDetail>(`/app-api/publishing/dy-push-plan/detail?id=${id}`)
    },
    draftDetail(id: number) {
      return request.get<DyPublishFormDetail>(`/app-api/publishing/dy-push-draft/detail?id=${id}`)
    },
  }
}

import React, { useCallback } from 'react'
import { z } from 'zod'
import { Input } from '@/components/ui/input'
import { <PERSON><PERSON><PERSON>oot<PERSON>, ModalHeader } from '@/components/modal'
import { Button } from '@/components/ui/button'
import { ChevronRight } from 'lucide-react'
import { genForm } from '@/libs/tools/form'
import { useModal, useModalContext } from '@/libs/tools/modal'
import { toast } from 'react-toastify'
import { usePending } from '@/hooks/usePending'

const DownloadForm = genForm(
  z.object({
    path: z
      .string()
      .min(1, '请选择下载路径')
      .refine(value => window.fileDownloader.fileExists(value), { message: '路径不存在' }),
  }),
  {
    fields: {
      path: {
        label: '下载路径',
        render: ({ field }) => (
          <div className="relative">
            <Input {...field} placeholder="请选择下载路径" className="pr-8" />
            <Button
              variant="ghost"
              size="icon"
              className="absolute top-1/2 -translate-y-1/2 right-1 size-6"
              onClick={async e => {
                e.stopPropagation()
                const folders = await window.fileDownloader.selectFolder()
                if (!folders || folders.length === 0) return
                field.onChange({ target: { value: folders[0] } })
              }}
            >
              <ChevronRight className="size-4" />
            </Button>
          </div>
        ),
      },
    },
  },
)

function DownloadModal({ files }: { files: { url: string; filename?: string }[] }) {
  const { pending, withPending } = usePending()
  const { close } = useModalContext()

  return (
    <>
      <ModalHeader title="下载至" description={`本次将下载 ${files.length} 个视频`} />
      <DownloadForm
        defaultValues={{ path: localStorage.getItem('PREVIOUS_DOWNLOAD_FOLDER') || '' }}
        onSubmit={withPending(async ({ path }) => {
          localStorage.setItem('PREVIOUS_DOWNLOAD_FOLDER', path)
          const params = files.map(file => ({
            url: file.url,
            path: `${path}/${file.filename || new URL(file.url).pathname.split('/').pop()}`,
          }))
          toast.promise(window.fileDownloader.downloadFiles(params), {
            pending: `正在下载 ${files.length} 个视频...`,
            success: '下载完成',
            error: '下载失败',
          })
          close()
        })}
      >
        <ModalFooter pending={pending} />
      </DownloadForm>
    </>
  )
}

export function useDownload() {
  const modal = useModal()

  return useCallback(
    (files: { url: string; filename?: string }[]) =>
      modal({
        content: <DownloadModal files={files} />,
      }),
    [],
  )
}

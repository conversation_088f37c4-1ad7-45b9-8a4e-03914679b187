#root {
  max-width: 1280px;
}

/* 默认允许文本选择，只对特定元素禁用 */
button, .app-drag-region, .no-select {
  user-select: none;
}

/* 明确允许文本选择的元素 */
.selectable, input, textarea, [contenteditable] {
  user-select: text;
}

/* 窗口拖动区域 */
.app-drag-region {
  -webkit-app-region: drag;
  app-region: drag;
}

/* 禁止拖动的区域（按钮等交互元素） */
.app-no-drag {
  -webkit-app-region: no-drag;
  app-region: no-drag;
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

/* Webkit浏览器 (Chrome, Safari, Edge等) */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #333;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #666;
  border-radius: 3px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.5);
}

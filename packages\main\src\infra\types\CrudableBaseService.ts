import { BaseEntity, PaginatedResult, PaginationParams, QueryConditions } from '@app/shared/types/database.types.js'
import { BaseRepository } from '@/infra/types/BaseRepository.js'

/**
 * 通用服务基类
 * 提供基本的CRUD操作和查询功能的服务层
 */
export abstract class CrudableBaseService<
  T extends BaseEntity,
  CreateParams = Partial<T>,
  UpdateParams = Partial<T>,
  QueryParams = QueryConditions,
  R extends BaseRepository<T, CreateParams, UpdateParams> = BaseRepository<T, CreateParams, UpdateParams>,
> {

  /**
   * 构造函数
   * @param repository 仓储实例
   */
  constructor(protected repository: R) {
  }

  /**
   * 创建记录
   * @param params 创建参数
   * @returns 创建的记录
   */
  async create(params: CreateParams): Promise<T> {
    return this.repository.create(params)
  }

  /**
   * 根据ID获取记录
   * @param id 记录ID
   * @param includeDeleted 是否包含已删除记录
   * @returns 记录或null
   */
  findById(id: any, includeDeleted = false): T | null {
    return this.repository.findById(id, includeDeleted)
  }

  /**
   * 查询记录列表
   * @param params 查询参数
   * @param pagination 分页参数
   * @returns 分页查询结果
   */
  findAll(params: QueryParams = {} as QueryParams, pagination: PaginationParams = {}): PaginatedResult<T> {
    return this.repository.findAll(params as QueryConditions, pagination)
  }

  /**
   * 更新记录
   * @param id 记录ID
   * @param params 更新参数
   * @returns 是否更新成功
   */
  update(id: any, params: UpdateParams): boolean {
    return this.repository.update(id, params)
  }

  /**
   * 软删除记录
   * @param id 记录ID
   * @returns 是否删除成功
   */
  delete(id: any): boolean {
    return this.repository.softDelete(id)
  }

  /**
   * 恢复已删除的记录
   * @param id 记录ID
   * @returns 是否恢复成功
   */
  restore(id: any): boolean {
    return this.repository.restore(id)
  }

  /**
   * 物理删除记录
   * @param id 记录ID
   * @returns 是否删除成功
   */
  hardDelete(id: any): boolean {
    return this.repository.hardDelete(id)
  }

  /**
   * 批量软删除记录
   * @param ids 记录ID数组
   * @returns 删除的记录数
   */
  batchDelete(ids: any[]): number {
    return this.repository.batchSoftDelete(ids)
  }

  /**
   * 批量物理删除记录
   * @param ids 记录ID数组
   * @returns 删除的记录数
   */
  batchHardDelete(ids: any[]): number {
    return this.repository.batchHardDelete(ids)
  }
}

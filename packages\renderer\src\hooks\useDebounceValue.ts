import { useState, useEffect, useMemo } from 'react'
import { debounce } from 'lodash'

/**
 * 用于值防抖的 Hook (基于 lodash debounce)
 * 当输入值频繁变化时，只有在指定延迟时间内没有新的变化时才更新防抖后的值
 *
 * @param value 需要防抖的值
 * @param delay 防抖延迟时间（毫秒）
 * @param options lodash debounce 选项
 * @returns 防抖后的值
 *
 * @example
 * ```tsx
 * const [searchTerm, setSearchTerm] = useState('')
 * const debouncedSearchTerm = useDebounceValue(searchTerm, 500)
 *
 * // 只有当用户停止输入 500ms 后，debouncedSearchTerm 才会更新
 * useEffect(() => {
 *   // 执行搜索
 *   search(debouncedSearchTerm)
 * }, [debouncedSearchTerm])
 * ```
 */
export function useDebounceValue<T>(
  value: T,
  delay: number,
  options?: {
    leading?: boolean
    trailing?: boolean
    maxWait?: number
  }
): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  // 使用 useRef 来保持 debounced 函数的引用稳定
  const debouncedFn = useMemo(
    () => debounce(
      (newValue: T) => setDebouncedValue(newValue),
      delay,
      options
    ),
    [delay, options]
  )

  useEffect(() => {
    debouncedFn(value)

    // 组件卸载时取消防抖函数
    return () => {
      debouncedFn.cancel()
    }
  }, [value, debouncedFn])

  return debouncedValue
}

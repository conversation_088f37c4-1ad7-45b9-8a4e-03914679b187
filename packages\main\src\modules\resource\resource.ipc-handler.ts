import { Inject, Injectable } from '@nestjs/common'
import { ResourceService } from './resource.service.js'
import { BaseIPCHandler } from '@/infra/types/BaseIPCHandler.js'

@Injectable()
export class ResourceIpcHandler extends BaseIPCHandler<'resource'> {

  protected readonly platformPrefix = 'resource'

  constructor(
    @Inject(ResourceService) private readonly service: ResourceService
  ) {
    super()
  }

  /**
   * 注册所有通用资源处理程序
   */
  registerAll(): void {
    // 通用资源获取，需要明确指定类型
    this.registerHandler('fetchOrSaveResource', async data => {
      return this.service.fetchOrSaveResource(data)
    })

    // 缓存清理
    this.registerHandler('cleanResource', async data => {
      return this.service.cleanResource(data)
    })

    // 获取资源信息
    this.registerHandler('getResource', async data => {
      return this.service.getResource(data)
    })

    // 获取资源路径（不下载）
    this.registerHandler('getResourcePath', async data => {
      return this.service.getResourcePath(data)
    })

    // 获取所有已缓存资源
    this.registerHandler('getAllResources', async () => {
      return this.service.getAllResources()
    })
  }
}

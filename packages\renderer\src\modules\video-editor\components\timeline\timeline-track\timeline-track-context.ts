import { createContext, useContext } from 'react'

import { IndexableTrack } from '@/modules/video-editor/types'

/**
 * Timeline Track Context 接口定义
 * 提供当前轨道的完整信息，使内部组件能够直接访问所在轨道的数据
 */
interface TimelineTrackContextType {
  /** 当前轨道的完整信息，包含轨道索引 */
  currentTrack: IndexableTrack
}

/**
 * Timeline Track Context
 * 用于在轨道组件内部共享轨道信息
 */
export const TimelineTrackContext = createContext<TimelineTrackContextType | null>(null)

/**
 * 使用 Timeline Track Context 的 Hook
 *
 * @returns 当前轨道的完整信息
 * @throws 如果在 TimelineTrackProvider 外部使用会抛出错误
 */
export const useTimelineTrackContext = (): TimelineTrackContextType => {
  const context = useContext(TimelineTrackContext)

  if (!context) {
    throw new Error('useTimelineTrackContext must be used within a TimelineTrackProvider')
  }

  return context
}

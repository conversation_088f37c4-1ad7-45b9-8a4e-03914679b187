import React, { Fragment, useMemo } from 'react'
import { AbsoluteFill, Sequence } from 'remotion'
import { LayerContent } from './layer-content'
import { OverlayType, RenderableOverlay } from '@clipnest/remotion-shared/types'

/**
 * Props for the Layer component
 * @property {Overlay} overlay - The overlay object containing position, dimensions, and content information
 * @property {string | undefined} baseUrl - The base URL for the video
 */
export const Layer: React.FC<{
  overlay: RenderableOverlay
  baseUrl?: string
}> = ({ overlay, baseUrl }) => {
  /**
   * Memoized style calculations for the layer
   * Handles positioning, dimensions, rotation, and z-index based on:
   * - Overlay position (left, top)
   * - Dimensions (width, height)
   * - Rotation
   * - Row position for z-index stacking
   * - Selection state for pointer events
   *
   * @returns {React.CSSProperties} Computed styles for the layer
   */
  const style: React.CSSProperties = useMemo(() => {
    return {
      position: 'absolute',
      left: overlay.left,
      top: overlay.top,
      width: overlay.width,
      height: overlay.height,
      transform: `rotate(${overlay.rotation || 0}deg)`,
      transformOrigin: 'center center',
      zIndex: overlay.zIndex,
    }
  }, [overlay])

  if (overlay.type === OverlayType.STORYBOARD) return null

  if (overlay.type === 'sound') {
    /**
     * Special handling for sound overlays
     * Sound overlays don't need positioning or visual representation,
     * they just need to be sequenced correctly
     */
    return (
      <Sequence
        key={overlay.id}
        from={overlay.from}
        durationInFrames={overlay.durationInFrames}
      >
        <LayerContent overlay={overlay} baseUrl={baseUrl} />
      </Sequence>
    )
  }

  return (
    <Fragment>
      <AbsoluteFill
        style={{
          overflow: 'hidden',
          maxWidth: '3000px',
        }}
      >
        <Sequence
          key={overlay.id}
          from={overlay.from}
          durationInFrames={overlay.durationInFrames}
          layout="none"
        >
          <div style={style}>
            <LayerContent overlay={overlay} baseUrl={baseUrl} />
          </div>
        </Sequence>
      </AbsoluteFill>
    </Fragment>
  )
}

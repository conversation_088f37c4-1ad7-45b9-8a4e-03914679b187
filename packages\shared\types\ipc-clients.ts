import { FolderIPCClient } from './ipc/crudables/folder.js'
import { MaterialFileIPCClient } from './ipc/crudables/material-file.js'
import { EditorIPCClient } from './ipc/editor.js'
import { TaskIPCClient } from './ipc/crudables/task.js'
import { ComposeIPCClient } from './ipc/crudables/compose.js'
import { WindowIPCClient } from './ipc/window.js'
import { ResourceIPCClient } from './ipc/resource.js'
import { FileDownloaderIPCClient, FileUploaderIPCClient } from './ipc/file-uploader.js'
import { BaseInfoIPCClient } from './ipc/base-info.js'

/**
 * Electron API 类型定义
 */
interface ElectronAPI {
  ipcRenderer: {
    on: (channel: string, callback: (...args: any[]) => void) => void
    removeListener: (channel: string, callback: (...args: any[]) => void) => void
    removeAllListeners: (channel: string) => void
  }
}

/**
 * IPC客户端类型定义
 */
export type IPCClients = {
  editor: EditorIPCClient

  windowManager: WindowIPCClient

  resource: ResourceIPCClient

  fileUploader: FileUploaderIPCClient

  fileDownloader: FileDownloaderIPCClient

  baseInfo: BaseInfoIPCClient

  electronAPI: ElectronAPI

  //#region ~ Crudable clients
  compose: ComposeIPCClient
  folder: FolderIPCClient
  materialFile: MaterialFileIPCClient
  task: TaskIPCClient
  //#endregion
}

export default IPCClients

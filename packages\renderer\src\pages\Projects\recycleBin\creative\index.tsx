import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>boxI<PERSON>, CheckboxProvider, useCheckboxContext } from '@/components/checkbox'
import { Button } from '@/components/ui/button'
import { useInfiniteQueryRemovedScriptList } from '@/hooks/queries/useQueryScript'
import { useIntersectionObserver } from '@/hooks/useIntersectionObserver'
import React, { useEffect } from 'react'
import { useParams } from 'react-router'
import { useProjectsContext } from '../../context'
import { Separator } from '@/components/ui/separator'
import { ResourceModule } from '@/libs/request/api/resource'
import { toast } from 'react-toastify'
import { useQueryClient } from '@tanstack/react-query'
import { QUERY_KEYS } from '@/constants/queryKeys'

export function CreativeRecycle() {
  return (
    <CheckboxProvider>
      <CreativeRecycleContent />
    </CheckboxProvider>
  )
}

function CreativeRecycleContent() {
  const queryClient = useQueryClient()
  const params = useParams()
  const { selected, all } = useCheckboxContext()
  const { keyword } = useProjectsContext()
  const { data, fetchNextPage, refetch } = useInfiniteQueryRemovedScriptList({ projectId: params.projectId, keyword })
  const scripts = React.useMemo(() => data?.pages.flatMap(v => v.list) || [], [data])
  const scrollRef = React.useRef<HTMLDivElement>(null)
  const NextPageFetcher = useIntersectionObserver({
    onIntersect: () => fetchNextPage(),
    deps: [data],
    root: scrollRef.current,
  })

  useEffect(() => {
    // FIXME: 不知道为什么会有这个问题，先清除一下，zzz
    queryClient.resetQueries({
      queryKey: [QUERY_KEYS.SCRIPT_REMOVED_LIST],
    })
  }, [])

  return (
    <div className="flex flex-col flex-1 overflow-auto gap-4">
      <div className="flex items-center justify-between text-sm text-muted-foreground h-8 px-2">
        <div className="flex items-center gap-2">
          <div className="flex items-center gap-2">
            <CheckboxHeader />
            <span>
              已选择：
              {Object.keys(selected).length}/{Object.keys(all).length}
            </span>
          </div>
          <Separator orientation="vertical" className="h-4 bg-muted-foreground/40" />
          <Button
            variant="default"
            size="sm"
            disabled={!Object.keys(selected).length}
            onClick={async () => {
              try {
                await ResourceModule.script.recover({ ids: Object.keys(selected).map(Number) })
                toast.success('还原成功')
                refetch()
              } catch (error: any) {
                toast.error(error?.message || '还原失败')
              }
            }}
          >
            批量还原
          </Button>
        </div>
        <div>文件在回收站保留15天，之后将被自动清除</div>
      </div>
      <div className="flex-1 overflow-y-auto flex flex-col gap-2" ref={scrollRef}>
        {scripts.map(script => (
          <div key={script.id} className="flex h-8 items-center hover:bg-muted-foreground/10 px-2 rounded">
            <CheckboxItem value={script.id} />
            <span className="ml-2 text-sm text-muted-foreground">{script.title}</span>
            <div className="ml-auto text-xs text-muted-foreground">
              <Button
                variant="link"
                size="sm"
                className="text-blue-500 hover:text-blue-500/90"
                onClick={async () => {
                  try {
                    await ResourceModule.script.recover({ ids: [script.id] })
                    toast.success('还原成功')
                    refetch()
                  } catch (error: any) {
                    toast.error(error?.message || '还原失败')
                  }
                }}
              >
                还原
              </Button>
            </div>
          </div>
        ))}
        <NextPageFetcher />
      </div>
    </div>
  )
}

import type { AxiosRequestConfig } from 'axios'

/**
 * 自定义请求配置，扩展 AxiosRequestConfig
 */
export interface RequestConfig extends AxiosRequestConfig {
  // 是否显示全局加载状态
  showLoading?: boolean
  // 是否显示错误提示
  showErrorMessage?: boolean
  // 是否显示成功提示
  showSuccessMessage?: boolean
  // 重试次数
  retryCount?: number
  // 重试延迟（毫秒）
  retryDelay?: number
}

/**
 * 错误信息类型
 */
export interface ErrorInfo {
  message: string
  code: string | number
  details?: any
}

/**
 * 请求方法类型
 */
export type RequestMethod = 'get' | 'post' | 'put' | 'delete' | 'patch'

/**
 * 请求函数类型
 */
export type RequestFunction = <T = any>(
  url: string,
  data?: any,
  config?: RequestConfig
) => Promise<T>

/**
 * HTTP 服务接口
 */
export interface HttpService {
  get: <T = any>(url: string, params?: any, config?: RequestConfig) => Promise<T>
  post: <T = any>(url: string, data?: any, config?: RequestConfig) => Promise<T>
  put: <T = any>(url: string, data?: any, config?: RequestConfig) => Promise<T>
  delete: <T = any>(url: string, params?: any, config?: RequestConfig) => Promise<T>
  patch: <T = any>(url: string, data?: any, config?: RequestConfig) => Promise<T>
  request: <T = any>(config: RequestConfig) => Promise<T>
}

import React from 'react'
import { VideoStylePanel } from './video-style-panel'
import { VideoSettingsPanel } from './video-settings-panel'
import { SettingsTabs, TabItem } from '../../shared/settings-tabs'
import { VideoScreenPanel } from './video-screen-panel'
import { VideoAdjustPanel } from './video-adjust-panel'

export const VideoSetting: React.FC = () => {
  const tabs: TabItem[] = [
    {
      value: 'screen',
      label: '画面',
      content: (
        <VideoScreenPanel  />
      )
    },
    {
      value: 'adjust',
      label: '调节',
      content: (
        <VideoAdjustPanel />
      )
    },
    {
      value: 'animation',
      label: '动画',
      content: (
        <VideoSettingsPanel />
      )
    },
    {
      value: 'style',
      label: '样式',
      content: (
        <VideoStylePanel />
      )
    }
  ]

  return (
    <div className="space-y-4 ">
      <SettingsTabs tabs={tabs} defaultTab="screen" />
    </div>
  )
}

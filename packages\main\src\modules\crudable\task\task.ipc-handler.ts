import { Inject, Injectable } from '@nestjs/common'
import { CrudableBase<PERSON>CHandler, IPCHandlerError } from '@/infra/types/CrudableBaseIPCHandler.js'
import { TaskService } from './task.service.js'

/**
 * 任务IPC处理器
 */
@Injectable()
export class TaskIPCHandler extends CrudableBaseIPCHandler<'task'> {

  /**
   * 构造函数
   */
  constructor(
    @Inject(TaskService)
    readonly taskService: TaskService
  ) {
    super(taskService, 'task')
  }

  /**
   * 注册额外的IPC处理程序
   */
  protected registerExtraHandlers(): void {
    this.registerHandler(
      'userTasks',
      async data => {
        if (!data) {
          throw new IPCHandlerError('获取用户任务参数不能为空')
        }
        if (!data.uid) {
          throw new IPCHandlerError('用户ID不能为空')
        }
        return this.taskService.getUserTasks(data.uid, data.status, data.teamId)
      },
    )

    this.registerHandler(
      'folderTasks',
      async data => {
        if (!data) {
          throw new IPCHandlerError('获取文件夹任务参数不能为空')
        }
        if (!data.folderId) {
          throw new IPCHandlerError('文件夹ID不能为空')
        }
        if (!data.uid) {
          throw new IPCHandlerError('用户ID不能为空')
        }
        return this.taskService.getTasksByFolder(data.folderId, data.uid, data.teamId)
      },
    )

    this.registerHandler(
      'tasksByType',
      async data => {
        if (!data) {
          throw new IPCHandlerError('获取类型任务参数不能为空')
        }
        if (!data.uid) {
          throw new IPCHandlerError('用户ID不能为空')
        }
        if (data.type === undefined || data.type === null) {
          throw new IPCHandlerError('任务类型不能为空')
        }
        return this.taskService.getTasksByType(data.uid, data.type, data.teamId)
      },
    )

    this.registerHandler(
      'search',
      async data => {
        if (!data) {
          throw new IPCHandlerError('搜索任务参数不能为空')
        }
        if (!data.keyword) {
          throw new IPCHandlerError('搜索关键词不能为空')
        }
        if (!data.uid) {
          throw new IPCHandlerError('用户ID不能为空')
        }
        return this.taskService.searchTasks(data.keyword, data.uid, data.teamId)
      },
    )

    this.registerHandler(
      'updateStatus',
      async data => {
        if (!data) {
          throw new IPCHandlerError('更新任务状态参数不能为空')
        }
        if (data.id === undefined || data.id === null) {
          throw new IPCHandlerError('任务ID不能为空')
        }
        if (data.status === undefined || data.status === null) {
          throw new IPCHandlerError('任务状态不能为空')
        }
        return this.taskService.updateTaskStatus(data.id, data.status, data.progress, data.reason)
      },
    )

    this.registerHandler(
      'batchMove',
      async data => {
        if (!data) {
          throw new IPCHandlerError('批量移动任务参数不能为空')
        }
        if (!data.ids || !Array.isArray(data.ids) || data.ids.length === 0) {
          throw new IPCHandlerError('任务ID数组不能为空')
        }
        if (!data.folderId) {
          throw new IPCHandlerError('目标文件夹ID不能为空')
        }
        return this.taskService.batchMoveTasks(data.ids, data.folderId)
      },
    )

    this.registerHandler(
      'getStats',
      async data => {
        if (!data) {
          throw new IPCHandlerError('获取任务统计数据参数不能为空')
        }
        if (!data.uid) {
          throw new IPCHandlerError('用户ID不能为空')
        }
        return this.taskService.getTaskStats(data.uid, data.teamId)
      },
    )
  }
}

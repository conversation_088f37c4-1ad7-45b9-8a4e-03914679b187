import React from 'react'
import { CategorySelector } from './category-selector'
import { CommonCategory } from '@/types/resources'

// 时长范围枚举
export enum DurationRange {
  ALL = 'all',
  SHORT = '0-10',      // 0-10秒
  MEDIUM = '10-30',    // 10-30秒
  LONG = '30-60',      // 30-60秒
  EXTRA_LONG = '60+'   // 60秒以上
}

// 时长范围配置
export const DURATION_RANGES: CommonCategory[] = [
  {
    id: DurationRange.SHORT,
    name: '0-10s',
    num: 0
  },
  {
    id: DurationRange.MEDIUM,
    name: '10-30s',
    num: 0
  },
  {
    id: DurationRange.LONG,
    name: '30-60s',
    num: 0
  },
  {
    id: DurationRange.EXTRA_LONG,
    name: '60s+',
    num: 0
  }
]

export interface DurationFilterProps {
  /**
   * 当前选中的时长范围
   */
  selectedDuration?: string
  /**
   * 时长范围变更回调
   */
  onDurationChange?: (duration: string) => void
  /**
   * 自定义类名
   */
  className?: string
  /**
   * 是否禁用
   */
  disabled?: boolean
}

/**
 * 音频时长筛选器组件
 * 提供预定义的时长范围选项进行筛选
 */
export function DurationFilter({
  selectedDuration,
  onDurationChange,
  className,
  disabled = false
}: DurationFilterProps) {
  return (
    <div className={className}>
      <CategorySelector
        options={DURATION_RANGES}
        value={selectedDuration}
        onValueChange={onDurationChange}
        disabled={disabled}
        showCollection={false}
        itemClassName="h-6"
      />
    </div>
  )
}

/**
 * 根据时长范围筛选音频数据的工具函数
 * @param items 音频数据列表
 * @param durationRange 选中的时长范围
 * @returns 筛选后的音频数据列表
 */
export function filterByDuration<T extends { content: { durationMsec: number } }>(
  items: T[],
  durationRange?: string
): T[] {
  if (!durationRange || durationRange === DurationRange.ALL) {
    return items
  }

  return items.filter(item => {
    const durationMs = item.content.durationMsec

    switch (durationRange) {
      case DurationRange.SHORT:
        return durationMs >= 0 && durationMs <= 10000
      case DurationRange.MEDIUM:
        return durationMs > 10000 && durationMs <= 30000
      case DurationRange.LONG:
        return durationMs > 30000 && durationMs <= 60000
      case DurationRange.EXTRA_LONG:
        return durationMs > 60000
      default:
        return true
    }
  })
}

/**
 * 获取时长范围的显示文本
 * @param durationRange 时长范围值
 * @returns 显示文本
 */
export function getDurationRangeLabel(durationRange?: string): string {
  const range = DURATION_RANGES.find(r => r.id === durationRange)
  return range?.name || '全部时长'
}

/**
 * 检查音频是否匹配指定的时长范围
 * @param durationMs 音频时长（毫秒）
 * @param durationRange 时长范围
 * @returns 是否匹配
 */
export function matchesDurationRange(durationMs: number, durationRange?: string): boolean {
  if (!durationRange || durationRange === DurationRange.ALL) {
    return true
  }

  switch (durationRange) {
    case DurationRange.SHORT:
      return durationMs >= 0 && durationMs <= 10000
    case DurationRange.MEDIUM:
      return durationMs > 10000 && durationMs <= 30000
    case DurationRange.LONG:
      return durationMs > 30000 && durationMs <= 60000
    case DurationRange.EXTRA_LONG:
      return durationMs > 60000
    default:
      return true
  }
}

export default DurationFilter

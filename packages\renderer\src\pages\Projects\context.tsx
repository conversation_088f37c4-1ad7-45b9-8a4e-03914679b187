import { Project } from '@/types/project'
import React, { createContext, useContext } from 'react'
import { useSearchParams } from 'react-router'

type ProjectsContextType = {
  keyword: string
  // HACK: 切换时将当前项目存储在 context 中，因为只提供了列表查询接口
  currentProject: Project | null
  setCurrentProject: (project: Project) => void
}

const Context = createContext<ProjectsContextType | null>(null)

export function ProjectsContextProvider({ children }) {
  const [searchParams] = useSearchParams()
  const [currentProject, setCurrentProject] = React.useState<Project | null>(null)

  const context: ProjectsContextType = {
    keyword: searchParams.get('keyword') || '',
    currentProject,
    setCurrentProject,
  }

  return <Context.Provider value={context}>{children}</Context.Provider>
}

export function useProjectsContext() {
  const context = useContext(Context)
  if (!context) {
    throw new Error('useProjectsContext must be used within a ProjectsContextProvider')
  }
  return context
}

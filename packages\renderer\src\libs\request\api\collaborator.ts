import { requestCurrying } from '../request'
import { Role } from './team'

export type Collaborator = {
  id: number
  projectId: number
  memberId: number
  mobile: string
  nickname: string
  avatar: string
  roles: Role[] | null
  source: 'manual'
  createTime: number
}

export const CollaboratorAPI = {
  list: requestCurrying.get<{ projectId: number }, Collaborator[]>('/app-api/creative/project-member/list'),
  save: requestCurrying.post<{ projectId: number; memberIds: number[] }, boolean>(
    '/app-api/creative/project-member/save',
  ),
}

import { Global, Module } from '@nestjs/common'
import { RequestService } from './request.service.js'
import { NestDatabaseService } from './database.service.js'
import { BaseInfoIPCHandler } from './base-info.ipc-handler.js'

@Module({
  providers: [
    BaseInfoIPCHandler,
    RequestService,
    {
      provide: NestDatabaseService,
      useFactory: () => new NestDatabaseService('clipnest.db', 'migrations')
    }
  ],
  exports: [
    RequestService,
    NestDatabaseService,
  ]
})
@Global()
export class GlobalModule {}

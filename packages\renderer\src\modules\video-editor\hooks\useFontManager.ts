import { useCallback, useRef } from 'react'
import { ResourceType } from '@app/shared/types/resource-cache.types'
import { useResource } from './resource/useResource'
import * as opentype from 'opentype.js'
import { cacheManager } from '@/libs/cache/cache-manager'
import { useResourceLoadingStore } from './resource/useResourceLoadingStore'
import { TextToSvgConvertor } from '@clipnest/overlay-renderer'

const LOCAL_PREFIX = ['/fonts/', './fonts/']

const LOG_PREFIX = '[字体管理]'

/**
 * 判断是否是本地字体文件（public 目录中的字体）
 */
function isLocalFont(src: string): boolean {
  return LOCAL_PREFIX.some(prefix => src.startsWith(prefix))
}

/**
 * 构建字体文件URL
 */
async function buildFontUrl(fontPath: string, downloadResourceToCache: any, version?: string): Promise<string> {
  if (isLocalFont(fontPath)) {
    return fontPath
  }

  // 处理远程字体文件
  let localPath = cacheManager.resource.getResourcePathSync(ResourceType.FONT, fontPath)

  if (!localPath) {
    localPath = await downloadResourceToCache({
      url: fontPath,
      resourceType: ResourceType.FONT,
      version: version ?? '1.0.0'
    })
  }

  if (!localPath) {
    throw new Error(`无法获取字体文件: ${fontPath}`)
  }

  return localPath.startsWith('http') ? localPath : `file://${localPath.replace(/\\/g, '/')}`
}

/**
 * 加载字体到 DOM
 */
async function loadFontToDOM(fontName: string, fontUrl: string): Promise<boolean> {
  try {
    const fontFace = new FontFace(fontName, `url("${fontUrl}")`)
    await fontFace.load()
    document.fonts.add(fontFace)
    return true
  } catch (error) {
    console.error(`${LOG_PREFIX} DOM 字体加载失败: ${fontName}`, error)
    throw error
  }
}

export function useFontManager() {
  const { downloadResourceToCache } = useResource()
  const { startResourceLoading, endResourceLoading, isResourceLoading } = useResourceLoadingStore()

  // 添加一个本地缓存来跟踪已处理的字体，防止重复处理
  const processedFonts = useRef(new Set<string>())

  /**
   * 检查字体是否已在 DOM 中加载
   * 结合内存缓存和 DOM 字体状态进行更准确的检查
   */
  const isFontStyleLoaded = useCallback((fontPath: string) => {
    // 首先检查内存缓存
    const cachedFont = cacheManager.font.getFont(fontPath)
    if (!cachedFont) {
      return false
    }

    // 然后检查 DOM 中的字体状态
    const loadedFonts = Array.from(document.fonts).filter(font => font.status === 'loaded')
    const isDOMLoaded = loadedFonts.some(font =>
      font.family === fontPath || (font as any).src?.includes(fontPath)
    )

    return isDOMLoaded
  }, [])

  /**
   * 加载字体并返回 opentype.Font 对象
   */
  const loadFontWithOpentype = useCallback(
    async (fontPath: string, fontName: string): Promise<opentype.Font> => {
    // 首先尝试从缓存获取
      const cachedFont = cacheManager.font.getFont(fontPath)
      if (cachedFont) {
        return cachedFont
      }

      // 检查是否正在加载
      // if (isResourceLoading(fontPath, ResourceType.FONT)) {
      //   console.log('Resource is Loading')
      //   return null
      // }

      // 开始加载状态
      startResourceLoading(fontPath, ResourceType.FONT)

      try {
        // 加载字体到缓存
        const font = await cacheManager.font.cacheFont(fontPath)

        if (font) {
          // 尝试同时加载到 DOM（失败不影响主要功能）
          try {
            const fontUrl = await buildFontUrl(fontPath, downloadResourceToCache)
            await loadFontToDOM(fontName, fontUrl)
          } catch (domError) {
            console.warn(`${LOG_PREFIX} DOM 字体加载失败，但 opentype.js 加载成功:`, domError)
          }
        }

        return font
      } catch (error) {
        console.error(`${LOG_PREFIX} 加载失败: ${fontName}`, error)
        throw error
      } finally {
      // 确保总是结束加载状态
        endResourceLoading(fontPath, ResourceType.FONT)
      }
    }, [startResourceLoading, endResourceLoading, isResourceLoading, downloadResourceToCache])

  /**
   * 计算文字尺寸的便捷方法
   */
  const calculateTextSize = useCallback(async (
    text: string = '默认文字',
    fontSize: number = 50,
    fontPath: string,
    fontName: string
  ): Promise<{ width: number; height: number } | null> => {
    try {
      // 加载字体并获取 opentype.Font 对象
      const font = await loadFontWithOpentype(fontPath, fontName)
      if (!font) {
        console.warn('[文字尺寸计算] 字体加载失败')
        return null
      }

      // 创建 TextToSvgConvertor 实例
      const convertor = new TextToSvgConvertor(font, font)

      // 计算尺寸
      const width = convertor._getWidth(text, fontSize)
      const height = convertor._getHeight(fontSize)

      return { width, height }
    } catch (error) {
      console.error('[文字尺寸计算] 计算失败:', error)
      return null
    }
  }, [loadFontWithOpentype])

  /**
   * 加载字体到 DOM（向后兼容）
   */
  const loadFont = useCallback(async (fontPath: string, fontName: string): Promise<boolean> => {
    // 防止重复处理同一个字体
    const fontKey = `${fontPath}:${fontName}`
    if (processedFonts.current.has(fontKey)) {
      console.debug(`${LOG_PREFIX} 字体已处理过，跳过: ${fontName}`)
      return true
    }

    // 首先检查字体是否已经完全加载（包括 DOM）
    if (isFontStyleLoaded(fontPath)) {
      console.debug(`${LOG_PREFIX} 字体已加载，跳过: ${fontName}`)
      processedFonts.current.add(fontKey)
      return true
    }

    // 检查是否正在加载
    if (isResourceLoading(fontPath, ResourceType.FONT)) {
      console.debug(`${LOG_PREFIX} 字体正在加载中，跳过: ${fontName}`)
      return false
    }

    // 检查是否已经下载到本地缓存
    const localPath = cacheManager.resource.getResourcePathSync(ResourceType.FONT, fontPath)
    if (localPath) {
      console.debug(`${LOG_PREFIX} 字体已下载，仅加载到 DOM: ${fontName}`)

      // 开始加载状态
      startResourceLoading(fontPath, ResourceType.FONT)

      try {
        const fontUrl = localPath.startsWith('http') ? localPath : `file://${localPath.replace(/\\/g, '/')}`
        const result = await loadFontToDOM(fontName, fontUrl)
        console.debug(`${LOG_PREFIX} 字体 DOM 加载完成: ${fontName}`)
        if (result) {
          processedFonts.current.add(fontKey)
        }
        return result
      } catch (error) {
        console.error(`${LOG_PREFIX} DOM 加载失败: ${fontName}`, error)
        return false
      } finally {
        endResourceLoading(fontPath, ResourceType.FONT)
      }
    }

    // 字体未下载，需要完整的下载和加载流程
    console.debug(`${LOG_PREFIX} 开始下载字体: ${fontName}`)

    // 开始加载状态
    startResourceLoading(fontPath, ResourceType.FONT)

    try {
      const fontUrl = await buildFontUrl(fontPath, downloadResourceToCache)

      if (isLocalFont(fontPath)) {
        console.debug(`${LOG_PREFIX} 加载本地字体到 DOM: ${fontPath}`)
      } else {
        console.debug(`${LOG_PREFIX} 下载并加载远程字体: ${fontName}`)
      }

      const result = await loadFontToDOM(fontName, fontUrl)
      if (result) {
        processedFonts.current.add(fontKey)
      }
      return result
    } catch (error) {
      console.error(`${LOG_PREFIX} 加载失败: ${fontName}`, error)
      return false
    } finally {
      // 结束加载状态
      endResourceLoading(fontPath, ResourceType.FONT)
    }
  }, [downloadResourceToCache, startResourceLoading, endResourceLoading, isResourceLoading, isFontStyleLoaded])

  return {
    isFontStyleLoaded,
    loadFont,
    calculateTextSize,
  }
}

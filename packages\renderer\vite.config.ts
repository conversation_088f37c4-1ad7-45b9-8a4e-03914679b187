import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'
import { resolve } from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    react(),
    tailwindcss(),
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),

      '@app/shared': resolve(__dirname, '../shared'),

      '@clipnest/overlay-renderer': resolve(__dirname, 'packages/overlay-renderer/src'),
      '@clipnest/remotion-shared': resolve(__dirname, 'packages/remotion-shared'),
    },
  },
  server: {
    host: '0.0.0.0',
    port: 5173,
  },
  build: {
    // 确保构建时使用相对路径
    assetsDir: 'assets',
    rollupOptions: {
      output: {
        // 确保资源文件使用相对路径
        assetFileNames: assetInfo => {
          const fileName = assetInfo.names?.[0] || assetInfo.originalFileNames?.[0] || 'unknown'
          // 保持字体文件在 fonts 目录
          if (fileName.endsWith('.ttf') || fileName.endsWith('.woff') || fileName.endsWith('.woff2')) {
            return 'fonts/[name].[ext]'
          }
          // 保持图片文件在 images 目录（如果需要）
          if (fileName.match(/\.(png|jpe?g|gif|svg)$/)) {
            return 'images/[name]-[hash].[ext]'
          }
          // 其他资源文件
          return 'assets/[name]-[hash].[ext]'
        }
      }
    }
  },
  // 使用相对路径作为基础路径
  base: './',
})

import React from 'react'
import { FolderO<PERSON> } from 'lucide-react'
import { OverlayType } from '@clipnest/remotion-shared/types'
import { registerMaterialPlugin } from '../registry'
import { ResourceType } from '@app/shared/types/resource-cache.types'

const Panel = React.lazy(() =>
  import('@/modules/video-editor/resource-plugin-system/plugin-panels/material-lib.panel')
)

export default registerMaterialPlugin({
  id: ResourceType.MATERIAL,
  title: '素材库',
  icon: FolderOpen,
  component: Panel,
  overlayType: OverlayType.material,
  order: 1,
})

